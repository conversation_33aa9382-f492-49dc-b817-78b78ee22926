import * as THREE from 'three';
import { SceneManager } from '../SceneManager';
import { useGlobalThreeStore } from '../../store/globalThreeStore';
import { ModelLoaderManager } from '../../lib/load/ModelLoaderManager';
import { containsDevice } from '../../utils/deviceIdentifier';
import { throttle } from 'lodash-es';

// 定义扩展的材质接口来处理THREE.js中的材质类型
interface ExtendedMaterial extends THREE.Material {
  map?: THREE.Texture;
  color?: THREE.Color;
  emissive?: THREE.Color;
  emissiveIntensity?: number;
  wireframe?: boolean;
}

interface DeviceData {
  id: string;
  position: THREE.Vector3;
  object: THREE.Object3D;
  scale: THREE.Vector3;
  boundingBox: THREE.Box3;
  size?: number; // 设备尺寸，用于动态调整纹理分辨率
}

interface MaterialOriginalProps {
  opacity: number;
  color: THREE.Color;
  emissive: THREE.Color;
  emissiveIntensity: number;
  wireframe: boolean;
}

// 纹理缓存接口
interface TextureCacheItem {
  texture: THREE.CanvasTexture;
  temperature: number;
  lastUsed: number;
  size: number;
}

// 性能监控接口
interface PerformanceMetrics {
  fps: number;
  lastFpsUpdateTime: number;
  frameCount: number;
  updateTimes: number[];
  deviceCount: number;
  textureCount: number;
  memoryUsage: number;
  qualityLevel: 'high' | 'medium' | 'low';
}

/**
 * 温度分布热力图管理器
 * 用于在3D场景中为设备创建温度分布热力图
 * 使用Canvas API直接绘制热力图效果
 */
export class HeatmapJSManager {
  private static instance: HeatmapJSManager | null = null;

  private sceneManager!: SceneManager;
  private scene!: THREE.Scene;
  public isActive: boolean = false;

  // 设备数据
  private deviceObjects: THREE.Object3D[] = []; // 存储设备对象
  private devicePositions: DeviceData[] = []; // 存储设备位置
  private deviceTemperatures: { [deviceId: string]: number } = {}; // 设备温度映射
  private heatmapMeshes: Map<string, THREE.Mesh> = new Map(); // 每个设备对应的热力图Mesh
  private originalMaterials: Map<THREE.Material, MaterialOriginalProps> = new Map(); // 设备原始材质

  // 用于场景透明化
  private nonDeviceMeshes: THREE.Mesh[] = []; // 非设备对象
  private nonDeviceMaterials: Map<THREE.Material, MaterialOriginalProps> = new Map(); // 非设备对象的原始材质
  private wallsTransparent: boolean = false; // 是否已透明化墙体
  private animationFrameIds: Set<number> = new Set(); // 动画帧ID集合，用于清理动画
  private isTransitioning: boolean = false; // 是否正在过渡
  private updateTimer: ReturnType<typeof setInterval> | null = null;

  // 纹理缓存系统
  private textureCache: Map<string, TextureCacheItem> = new Map(); // 纹理缓存
  private maxCacheSize: number = 50; // 最大缓存数量
  private textureSizes: { high: number; medium: number; low: number } = { high: 512, medium: 256, low: 128 }; // 纹理尺寸配置

  // 性能监控
  private performanceMetrics: PerformanceMetrics = {
    fps: 0,
    lastFpsUpdateTime: 0,
    frameCount: 0,
    updateTimes: [],
    deviceCount: 0,
    textureCount: 0,
    memoryUsage: 0,
    qualityLevel: 'medium',
  };

  // 批处理设置
  private batchSize: number = 10; // 每批处理的设备数量
  private updateInterval: number = 5000; // 更新间隔（毫秒）

  // 获取单例实例
  public static getInstance(): HeatmapJSManager {
    if (!HeatmapJSManager.instance) {
      HeatmapJSManager.instance = new HeatmapJSManager();
    }
    return HeatmapJSManager.instance;
  }

  // 私有构造函数，确保单例模式
  private constructor() {
    this.initialize();
  }

  // 初始化
  private initialize(): void {
    try {
      this.sceneManager = SceneManager.getInstance();
      // 不在初始化时获取scene，而是在需要时获取
      // 因为scene可能在初始化时还未准备好

      // 初始化性能监控
      this.initPerformanceMonitoring();

      console.log('HeatmapJSManager 初始化完成');
    } catch (error) {
      console.error('HeatmapJSManager 初始化失败:', error);
    }
  }

  // 获取当前场景
  private getScene(): THREE.Scene {
    if (!this.scene) {
      // SceneManager 直接暴露 scene 属性，而不是通过 getScene 方法
      this.scene = this.sceneManager.scene;
      if (!this.scene) {
        throw new Error('无法获取场景对象');
      }
    }
    return this.scene;
  }

  // 初始化性能监控
  private initPerformanceMonitoring(): void {
    // 使用requestAnimationFrame监控FPS
    const updateFPS = () => {
      this.performanceMetrics.frameCount++;
      const now = performance.now();
      const elapsed = now - this.performanceMetrics.lastFpsUpdateTime;

      if (elapsed >= 1000) {
        this.performanceMetrics.fps = Math.round((this.performanceMetrics.frameCount * 1000) / elapsed);
        this.performanceMetrics.frameCount = 0;
        this.performanceMetrics.lastFpsUpdateTime = now;
      }

      if (this.isActive) {
        const frameId = requestAnimationFrame(updateFPS);
        this.animationFrameIds.add(frameId);
      }
    };

    updateFPS();
  }

  // 获取当前质量级别的纹理尺寸
  private getTextureSizeForQuality(): number {
    return this.textureSizes[this.performanceMetrics.qualityLevel];
  }

  // 清理纹理缓存
  private cleanupTextureCache(): void {
    if (this.textureCache.size <= this.maxCacheSize) return;

    // 按最后使用时间排序
    const entries = Array.from(this.textureCache.entries()).sort((a, b) => a[1].lastUsed - b[1].lastUsed);

    // 移除最旧的25%
    const removeCount = Math.ceil(this.textureCache.size * 0.25);
    for (let i = 0; i < removeCount; i++) {
      if (entries[i]) {
        const [key, item] = entries[i];
        item.texture.dispose();
        this.textureCache.delete(key);
      }
    }

    console.log(`已清理 ${removeCount} 个纹理缓存项`);
  }

  // 创建热力图纹理 - 使用Canvas直接绘制热力图效果
  private createHeatmapTexture(temperature: number, deviceSize: number = 1): THREE.Texture {
    try {
      // 量化温度值，减少缓存变体
      const roundedTemp = Math.round(temperature * 2) / 2; // 四舍五入到0.5

      // 获取当前质量级别的纹理尺寸
      const baseSize = this.getTextureSizeForQuality();

      // 根据设备大小调整纹理尺寸，但限制在一定范围内
      const sizeMultiplier = Math.max(0.5, Math.min(1.5, deviceSize));
      const size = Math.max(64, Math.min(512, Math.round(baseSize * sizeMultiplier)));

      // 生成缓存键
      const cacheKey = `temp_${roundedTemp}_size_${size}`;

      // 检查缓存
      if (this.textureCache.has(cacheKey)) {
        const cachedItem = this.textureCache.get(cacheKey)!;
        cachedItem.lastUsed = performance.now();
        return cachedItem.texture;
      }

      // 如果缓存过大，清理
      this.cleanupTextureCache();

      // 创建Canvas
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法获取Canvas 2D上下文');
      }

      try {
        // 清除画布
        ctx.clearRect(0, 0, size, size);

        // 计算温度值的相对强度 (18°C - 40°C)
        const normalizedValue = Math.max(0, Math.min(1, (roundedTemp - 18) / 22));

        // 获取温度对应的颜色
        let color: string;
        if (normalizedValue < 0.25) {
          // 蓝色到青色
          const r = 0;
          const g = Math.round(normalizedValue * 4 * 255);
          const b = 255;
          color = `rgb(${r}, ${g}, ${b})`;
        } else if (normalizedValue < 0.5) {
          // 青色到绿色
          const r = 0;
          const g = 255;
          const b = Math.round((1 - (normalizedValue - 0.25) * 4) * 255);
          color = `rgb(${r}, ${g}, ${b})`;
        } else if (normalizedValue < 0.75) {
          // 绿色到黄色
          const r = Math.round((normalizedValue - 0.5) * 4 * 255);
          const g = 255;
          const b = 0;
          color = `rgb(${r}, ${g}, ${b})`;
        } else {
          // 黄色到红色
          const r = 255;
          const g = Math.round((1 - (normalizedValue - 0.75) * 4) * 255);
          const b = 0;
          color = `rgb(${r}, ${g}, ${b})`;
        }

        // 绘制中心热点
        const centerGradient = ctx.createRadialGradient(size / 2, size / 2, 0, size / 2, size / 2, size / 2);

        centerGradient.addColorStop(0, color.replace('rgb', 'rgba').replace(')', ', 0.7)'));
        centerGradient.addColorStop(0.3, color.replace('rgb', 'rgba').replace(')', ', 0.4)'));
        centerGradient.addColorStop(0.7, color.replace('rgb', 'rgba').replace(')', ', 0.1)'));
        centerGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.fillStyle = centerGradient;
        ctx.fillRect(0, 0, size, size);

        // 添加一些随机热点，使热力图更自然
        for (let i = 0; i < 5; i++) {
          const distance = Math.random() * (size / 3);
          const angle = Math.random() * Math.PI * 2;
          const x = size / 2 + Math.cos(angle) * distance;
          const y = size / 2 + Math.sin(angle) * distance;
          const radius = (size / 4) * (0.3 + Math.random() * 0.4);

          const spotGradient = ctx.createRadialGradient(x, y, 0, x, y, radius);

          const opacity = 0.3 + Math.random() * 0.4;
          spotGradient.addColorStop(0, color.replace('rgb', 'rgba').replace(')', `, ${opacity})`));
          spotGradient.addColorStop(0.6, color.replace('rgb', 'rgba').replace(')', ', 0.1)'));
          spotGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

          ctx.fillStyle = spotGradient;
          ctx.fillRect(0, 0, size, size);
        }

        // 创建Three.js纹理
        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;

        // 优化纹理设置
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.generateMipmaps = false;

        // 缓存纹理
        this.textureCache.set(cacheKey, {
          texture,
          temperature: roundedTemp,
          lastUsed: performance.now(),
          size,
        });

        // 更新性能指标
        this.performanceMetrics.textureCount = this.textureCache.size;

        return texture;
      } catch (error) {
        console.error('创建热力图失败:', error);
        // 返回备用纹理
        return this.createFallbackTexture(temperature);
      }
    } catch (error) {
      console.error('创建热力图纹理时发生错误:', error);
      return this.createFallbackTexture(temperature);
    }
  }

  // 创建备用纹理 - 当heatmap.js失败时使用
  private createFallbackTexture(temperature: number): THREE.Texture {
    const size = this.getTextureSizeForQuality();
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('无法获取Canvas 2D上下文');
      // 创建一个1x1的纯色纹理作为最后的备用
      const fallbackTexture = new THREE.CanvasTexture(canvas);
      fallbackTexture.needsUpdate = true;
      return fallbackTexture;
    }

    // 清除画布
    ctx.clearRect(0, 0, size, size);

    // 创建径向渐变
    const gradient = ctx.createRadialGradient(size / 2, size / 2, 0, size / 2, size / 2, size / 2);

    // 根据温度获取颜色
    const t = Math.max(0, Math.min(1, (temperature - 18) / 22));
    let color;

    if (t < 0.25) {
      color = `rgb(0, ${Math.round(t * 4 * 255)}, 255)`; // 蓝色到青色
    } else if (t < 0.5) {
      color = `rgb(0, 255, ${Math.round((1 - (t - 0.25) * 4) * 255)})`; // 青色到绿色
    } else if (t < 0.75) {
      color = `rgb(${Math.round((t - 0.5) * 4 * 255)}, 255, 0)`; // 绿色到黄色
    } else {
      color = `rgb(255, ${Math.round((1 - (t - 0.75) * 4) * 255)}, 0)`; // 黄色到红色
    }

    // 设置渐变
    gradient.addColorStop(0, color.replace('rgb', 'rgba').replace(')', ', 0.6)'));
    gradient.addColorStop(0.4, color.replace('rgb', 'rgba').replace(')', ', 0.3)'));
    gradient.addColorStop(0.8, 'rgba(0, 0, 0, 0)');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.generateMipmaps = false;

    return texture;
  }

  // 收集当前楼层内的设备
  private async collectDevices(): Promise<void> {
    try {
      // 重置设备数据
      this.deviceObjects = [];
      this.devicePositions = [];

      // 记录开始时间，用于性能监控
      const startTime = performance.now();

      const modelLoader = ModelLoaderManager.getInstance();
      const models = modelLoader.getCurrentModels();

      // 限制最大设备数量，防止性能问题
      const maxDevices = 100;
      let deviceCount = 0;

      // 使用Set避免重复处理同一设备
      const processedIds = new Set<string>();

      // 记录找到的设备和跳过的设备
      const foundDevices: string[] = [];
      const skippedDevices: string[] = [];

      console.log('开始收集设备，使用正则表达式: /^([1-6]F|F[1-6])/');
      console.log('当前模型数量:', models.length);

      for (const model of models) {
        if (model.userData.type !== 'floor' || deviceCount >= maxDevices) continue;

        // 使用队列进行广度优先遍历，避免递归导致的栈溢出
        const queue: THREE.Object3D[] = [model];

        while (queue.length > 0 && deviceCount < maxDevices) {
          const object = queue.shift()!;

          // 避免重复处理
          if (processedIds.has(object.uuid)) continue;
          processedIds.add(object.uuid);

          // 添加子对象到队列
          for (const child of object.children) {
            queue.push(child);
          }

          // 检查是否为设备 - 使用动画循环优化版本
          if (object instanceof THREE.Mesh && containsDevice(object, true)) {
            this.deviceObjects.push(object);
            deviceCount++;
            foundDevices.push(object.name);

            const position = new THREE.Vector3();
            object.getWorldPosition(position);

            // 获取对象的缩放
            const scale = new THREE.Vector3();
            object.getWorldScale(scale);

            // 计算设备尺寸，用于后续优化
            const boundingBox = new THREE.Box3().setFromObject(object);
            const size = new THREE.Vector3();
            boundingBox.getSize(size);
            const deviceSize = Math.max(size.x, size.z);

            this.devicePositions.push({
              id: object.name,
              position,
              boundingBox,
              object,
              scale,
              size: deviceSize,
            });

            // 为每个设备生成随机温度(28-40°C)
            this.deviceTemperatures[object.name] = 28 + Math.random() * 12;
          } else if (object instanceof THREE.Mesh) {
            // 记录跳过的设备
            skippedDevices.push(object.name);
          }
        }
      }

      // 记录性能指标
      const endTime = performance.now();
      this.performanceMetrics.deviceCount = this.devicePositions.length;

      console.log(`设备收集完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
      console.log(`找到符合条件的设备: ${this.devicePositions.length}个`);
      console.log(`找到的设备: ${foundDevices.slice(0, 10).join(', ')}${foundDevices.length > 10 ? '...' : ''}`);
      console.log(`跳过的设备(部分): ${skippedDevices.slice(0, 10).join(', ')}${skippedDevices.length > 10 ? '...' : ''}`);
    } catch (error) {
      console.error('收集设备时发生错误:', error);
      throw error;
    }
  }

  // 为单个设备创建热力图
  private createHeatmapForDevice(deviceData: DeviceData): void {
    try {
      // 确保场景已初始化
      if (!this.scene) {
        console.warn('场景未初始化，无法创建热力图');
        return;
      }

      const { id, position, boundingBox, size = 1 } = deviceData;
      const temperature = this.deviceTemperatures[id] || 30;

      // 根据设备尺寸调整热力图大小
      const sizeVector = new THREE.Vector3();
      boundingBox.getSize(sizeVector);
      const heatmapSize = Math.max(sizeVector.x, sizeVector.z) * 4;

      // 使用共享几何体，减少内存占用
      const geometry = new THREE.PlaneGeometry(heatmapSize, heatmapSize);
      geometry.rotateX(-Math.PI / 2); // 水平放置

      try {
        // 创建纹理
        const texture = this.createHeatmapTexture(temperature, size);

        // 创建材质
        const material = new THREE.MeshBasicMaterial({
          map: texture,
          transparent: true,
          depthWrite: false,
          side: THREE.DoubleSide,
        });

        const heatmapMesh = new THREE.Mesh(geometry, material);

        // 稍微提高热力图位置，确保在地面上方显示
        const bottomY = boundingBox.min.y;
        heatmapMesh.position.set(position.x, bottomY + 0.05, position.z);

        // 优化渲染性能
        heatmapMesh.matrixAutoUpdate = false;
        heatmapMesh.updateMatrix();

        // 添加到场景
        this.heatmapMeshes.set(id, heatmapMesh);
        this.scene.add(heatmapMesh);

        // 记录成功
        if (Math.random() < 0.1) {
          // 只记录约10%的成功案例，避免日志过多
          console.debug(`成功为设备 ${id} 创建热力图，温度: ${temperature.toFixed(1)}°C`);
        }
      } catch (textureError) {
        console.warn(`为设备 ${id} 创建纹理失败，尝试使用纯色热力图:`, textureError);

        // 尝试创建一个简单的纯色平面作为备用
        try {
          const color = this.getTemperatureColor(temperature);
          const material = new THREE.MeshBasicMaterial({
            color: new THREE.Color(color),
            transparent: true,
            opacity: 0.5,
            depthWrite: false,
            side: THREE.DoubleSide,
          });

          const heatmapMesh = new THREE.Mesh(geometry, material);
          const bottomY = boundingBox.min.y;
          heatmapMesh.position.set(position.x, bottomY + 0.05, position.z);
          heatmapMesh.matrixAutoUpdate = false;
          heatmapMesh.updateMatrix();

          this.heatmapMeshes.set(id, heatmapMesh);
          this.scene.add(heatmapMesh);

          console.debug(`已为设备 ${id} 创建备用纯色热力图`);
        } catch (fallbackError) {
          console.error(`创建备用纯色热力图也失败:`, fallbackError);
        }
      }
    } catch (error) {
      console.error(`为设备 ${deviceData?.id || '未知'} 创建热力图时发生错误:`, error);
    }
  }

  // 根据温度获取颜色
  private getTemperatureColor(temperature: number): string {
    // 计算温度值的相对强度 (18°C - 40°C)
    const normalizedValue = Math.max(0, Math.min(1, (temperature - 18) / 22));

    let color: string;
    if (normalizedValue < 0.25) {
      // 蓝色到青色
      const r = 0;
      const g = Math.round(normalizedValue * 4 * 255);
      const b = 255;
      color = `rgb(${r}, ${g}, ${b})`;
    } else if (normalizedValue < 0.5) {
      // 青色到绿色
      const r = 0;
      const g = 255;
      const b = Math.round((1 - (normalizedValue - 0.25) * 4) * 255);
      color = `rgb(${r}, ${g}, ${b})`;
    } else if (normalizedValue < 0.75) {
      // 绿色到黄色
      const r = Math.round((normalizedValue - 0.5) * 4 * 255);
      const g = 255;
      const b = 0;
      color = `rgb(${r}, ${g}, ${b})`;
    } else {
      // 黄色到红色
      const r = 255;
      const g = Math.round((1 - (normalizedValue - 0.75) * 4) * 255);
      const b = 0;
      color = `rgb(${r}, ${g}, ${b})`;
    }

    return color;
  }

  // 批量创建热力图
  private createHeatmapsForDevices(): void {
    try {
      // 记录开始时间
      const startTime = performance.now();

      // 分批处理设备，避免长时间阻塞主线程
      const processBatch = (startIndex: number) => {
        const endIndex = Math.min(startIndex + this.batchSize, this.devicePositions.length);

        for (let i = startIndex; i < endIndex; i++) {
          this.createHeatmapForDevice(this.devicePositions[i]);
        }

        // 如果还有剩余设备，继续处理下一批
        if (endIndex < this.devicePositions.length) {
          setTimeout(() => processBatch(endIndex), 0);
        } else {
          // 所有批次处理完成
          const endTime = performance.now();
          console.log(`热力图创建完成，总耗时: ${(endTime - startTime).toFixed(2)}ms`);
        }
      };

      // 开始处理第一批
      processBatch(0);
    } catch (error) {
      console.error('批量创建热力图时发生错误:', error);
    }
  }

  // 更新单个设备的热力图
  private updateHeatmapForDevice(deviceData: DeviceData): void {
    try {
      const { id, size = 1 } = deviceData;
      const heatmapMesh = this.heatmapMeshes.get(id);

      if (!heatmapMesh) return;

      // 更新温度 - 随机波动，但幅度减小
      let temperature = this.deviceTemperatures[id] || 30;
      temperature += (Math.random() - 0.5) * 1; // 随机波动±0.5度
      temperature = Math.max(18, Math.min(40, temperature));
      this.deviceTemperatures[id] = temperature;

      // 使用heatmap.js创建新纹理
      const texture = this.createHeatmapTexture(temperature, size);

      // 更新材质
      const material = heatmapMesh.material as THREE.MeshBasicMaterial;
      if (material.map) {
        material.map.dispose(); // 释放旧纹理
      }
      material.map = texture;
      material.needsUpdate = true;
    } catch (error) {
      console.error(`更新设备 ${deviceData.id} 的热力图时发生错误:`, error);
    }
  }

  // 批量更新热力图
  private updateHeatmaps(): void {
    try {
      // 记录开始时间
      const startTime = performance.now();

      // 分批处理设备，避免长时间阻塞主线程
      const processBatch = (startIndex: number) => {
        const endIndex = Math.min(startIndex + this.batchSize, this.devicePositions.length);

        for (let i = startIndex; i < endIndex; i++) {
          this.updateHeatmapForDevice(this.devicePositions[i]);
        }

        // 如果还有剩余设备，继续处理下一批
        if (endIndex < this.devicePositions.length) {
          setTimeout(() => processBatch(endIndex), 0);
        } else {
          // 所有批次处理完成
          const endTime = performance.now();

          // 记录更新时间，用于性能监控
          this.performanceMetrics.updateTimes.push(endTime - startTime);
          if (this.performanceMetrics.updateTimes.length > 10) {
            this.performanceMetrics.updateTimes.shift();
          }

          // 计算平均更新时间
          const avgUpdateTime = this.performanceMetrics.updateTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.updateTimes.length;

          // 根据平均更新时间动态调整质量
          if (avgUpdateTime > 100 && this.performanceMetrics.qualityLevel !== 'low') {
            console.warn(`更新时间过长(${avgUpdateTime.toFixed(2)}ms)，降低质量级别`);
            this.performanceMetrics.qualityLevel = 'low';
          } else if (avgUpdateTime < 50 && this.performanceMetrics.qualityLevel === 'low') {
            console.log(`更新时间良好(${avgUpdateTime.toFixed(2)}ms)，提高质量级别`);
            this.performanceMetrics.qualityLevel = 'medium';
          }
        }
      };

      // 开始处理第一批
      processBatch(0);
    } catch (error) {
      console.error('批量更新热力图时发生错误:', error);
    }
  }

  // 开始定时更新
  private startUpdating(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }

    this.updateTimer = setInterval(() => {
      if (this.isActive) {
        this.updateHeatmaps();
      }
    }, this.updateInterval);
  }

  // 停止定时更新
  private stopUpdating(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
  }

  // 清理热力图
  private clearHeatmaps(): void {
    console.log('[HeatmapJSManager] 开始清理温度分布热力图资源');

    try {
      // 确保场景已初始化
      if (!this.scene) {
        console.warn('[HeatmapJSManager] 场景未初始化，无法清理热力图');
        return;
      }

      // 从场景中移除所有热力图
      let removedMeshCount = 0;
      let disposedMaterialCount = 0;
      let disposedGeometryCount = 0;

      for (const [id, mesh] of this.heatmapMeshes.entries()) {
        try {
          this.scene.remove(mesh);
          removedMeshCount++;

          // 释放材质和几何体
          if (mesh.material) {
            const material = mesh.material as THREE.MeshBasicMaterial;
            if (material.map) {
              material.map.dispose();
            }
            material.dispose();
            disposedMaterialCount++;
          }

          if (mesh.geometry) {
            mesh.geometry.dispose();
            disposedGeometryCount++;
          }
        } catch (meshError) {
          console.warn(`[HeatmapJSManager] 清理热力图 ${id} 时发生错误:`, meshError);
        }
      }

      // 清空映射
      const meshCount = this.heatmapMeshes.size;
      this.heatmapMeshes.clear();

      // 清理纹理缓存
      let disposedTextureCount = 0;
      for (const [key, item] of this.textureCache.entries()) {
        try {
          item.texture.dispose();
          disposedTextureCount++;
        } catch (textureError) {
          console.warn(`[HeatmapJSManager] 清理纹理缓存 ${key} 时发生错误:`, textureError);
        }
      }

      const textureCount = this.textureCache.size;
      this.textureCache.clear();

      console.log(
        `[HeatmapJSManager] 温度分布热力图已清理: 移除了 ${removedMeshCount}/${meshCount} 个网格, 释放了 ${disposedMaterialCount} 个材质, ${disposedGeometryCount} 个几何体, ${disposedTextureCount}/${textureCount} 个纹理`
      );

      // 清理设备数据
      const deviceCount = this.devicePositions.length;
      this.devicePositions = [];
      this.deviceObjects = [];
      this.deviceTemperatures = {};

      console.log(`[HeatmapJSManager] 已清理 ${deviceCount} 个设备数据`);
    } catch (error) {
      console.error('[HeatmapJSManager] 清理热力图时发生错误:', error);

      // 尝试强制清理
      try {
        this.heatmapMeshes.clear();
        this.textureCache.clear();
        this.devicePositions = [];
        this.deviceObjects = [];
        this.deviceTemperatures = {};
        console.log('[HeatmapJSManager] 已强制清理所有热力图资源');
      } catch (forceCleanError) {
        console.error('[HeatmapJSManager] 强制清理热力图资源时发生错误:', forceCleanError);
      }
    }
  }

  // 检查系统支持
  private checkSystemSupport(): void {
    try {
      // 检查WebGL支持
      if (!this.sceneManager.renderer) {
        console.warn('未找到渲染器，热力图功能可能无法正常工作');
      }

      // 检查Canvas支持
      const canvas = document.createElement('canvas');
      if (!canvas.getContext('2d')) {
        console.warn('浏览器不支持Canvas 2D，热力图功能可能无法正常工作');
      }

      // 检查场景是否可用
      if (!this.sceneManager.scene) {
        console.warn('场景对象不可用，热力图功能可能无法正常工作');
      }
    } catch (error) {
      console.warn('检查系统支持时发生错误:', error);
    }
  }

  // 公开方法：显示热力图
  public async show(): Promise<void> {
    console.log('[HeatmapJSManager] 请求显示温度分布热力图');

    try {
      if (this.isActive) {
        console.log('[HeatmapJSManager] 温度分布热力图已处于活动状态，无需重新显示');
        return;
      }

      // 检查是否有其他热力图功能处于活动状态
      this.checkOtherHeatmapManagers();

      // 检查系统支持
      this.checkSystemSupport();

      // 确保场景已初始化
      if (!this.scene) {
        console.log('[HeatmapJSManager] 正在初始化场景...');
        try {
          this.scene = this.sceneManager.scene;
          if (!this.scene) {
            throw new Error('无法获取场景对象');
          }
        } catch (sceneError) {
          console.error('[HeatmapJSManager] 初始化场景失败:', sceneError);
          throw new Error('无法初始化场景，热力图功能无法使用');
        }
      }

      // 设置初始状态
      this.isActive = true;
      this.performanceMetrics.qualityLevel = 'medium';

      console.log('[HeatmapJSManager] 正在加载温度分布热力图功能...');

      try {
        // 收集设备
        console.log('[HeatmapJSManager] 开始收集设备...');
        await this.collectDevices();

        if (this.devicePositions.length === 0) {
          console.warn('[HeatmapJSManager] 未找到可应用热力图的设备');
          this.isActive = false;
          return;
        }

        // 如果设备数量过多，自动降低质量
        if (this.devicePositions.length > 50) {
          console.warn(`[HeatmapJSManager] 设备数量较多(${this.devicePositions.length})，自动降低热力图质量以保持性能`);
          this.performanceMetrics.qualityLevel = 'low';
        }

        // 创建热力图
        console.log(`[HeatmapJSManager] 开始创建热力图，设备数量: ${this.devicePositions.length}，质量级别: ${this.performanceMetrics.qualityLevel}`);
        this.createHeatmapsForDevices();

        // 开始定时更新
        this.startUpdating();

        console.log(
          `[HeatmapJSManager] 温度分布热力图已显示，质量级别: ${this.performanceMetrics.qualityLevel}, 设备数量: ${this.devicePositions.length}`
        );
      } catch (innerError) {
        // 内部操作失败时，尝试回退到低质量模式
        console.warn('[HeatmapJSManager] 热力图创建过程中出错，尝试使用低质量模式:', innerError);

        // 清理已创建的资源
        this.clearHeatmaps();

        // 尝试使用低质量模式
        this.performanceMetrics.qualityLevel = 'low';

        try {
          // 重新收集设备，但限制数量
          console.log('[HeatmapJSManager] 使用低质量模式重新收集设备...');
          await this.collectDevices();

          if (this.devicePositions.length === 0) {
            throw new Error('未找到可应用热力图的设备');
          }

          // 使用更小的批次大小
          this.batchSize = 5;

          // 重新创建热力图
          console.log(`[HeatmapJSManager] 使用低质量模式创建热力图，设备数量: ${this.devicePositions.length}`);
          this.createHeatmapsForDevices();

          // 开始定时更新，但使用更长的间隔
          this.updateInterval = 8000;
          this.startUpdating();

          console.log(`[HeatmapJSManager] 温度分布热力图已以低质量模式显示，设备数量: ${this.devicePositions.length}`);
        } catch (fallbackError) {
          // 如果回退也失败，则放弃
          console.error('[HeatmapJSManager] 低质量模式也失败，无法显示热力图:', fallbackError);
          throw fallbackError;
        }
      }
    } catch (error) {
      // 出错时恢复状态
      this.isActive = false;
      this.clearHeatmaps();
      this.stopUpdating();

      console.error('[HeatmapJSManager] 显示温度分布热力图时发生错误:', error);
      throw error;
    }
  }

  // 检查其他热力图管理器是否处于活动状态
  private checkOtherHeatmapManagers(): void {
    try {
      // 检查HeatmapManager
      const heatManager = (window as any).HeatmapManager || (globalThis as any).HeatmapManager;

      if (heatManager && typeof heatManager.getInstance === 'function') {
        const instance = heatManager.getInstance();
        if (instance && instance.currentEffect) {
          console.warn('[HeatmapJSManager] 检测到其他热力图功能处于活动状态，这可能导致冲突');
        }
      }
    } catch (error) {
      console.warn('[HeatmapJSManager] 检查其他热力图管理器时出错:', error);
    }
  }

  // 公开方法：隐藏热力图
  public hide(): void {
    console.log('[HeatmapJSManager] 请求隐藏温度分布热力图');

    if (!this.isActive) {
      console.log('[HeatmapJSManager] 温度分布热力图未处于活动状态，无需隐藏');
      return;
    }

    try {
      // 停止更新
      console.log('[HeatmapJSManager] 停止热力图更新...');
      this.stopUpdating();

      // 清理热力图
      console.log('[HeatmapJSManager] 清理热力图资源...');
      this.clearHeatmaps();

      // 更新状态
      this.isActive = false;

      console.log('[HeatmapJSManager] 温度分布热力图已隐藏，所有资源已释放');
    } catch (error) {
      console.error('[HeatmapJSManager] 隐藏温度分布热力图时发生错误:', error);

      // 确保状态被重置
      this.isActive = false;

      // 尝试强制清理
      try {
        this.stopUpdating();
        this.clearHeatmaps();
      } catch (cleanupError) {
        console.error('[HeatmapJSManager] 强制清理资源时发生错误:', cleanupError);
      }
    }
  }

  // 公开方法：切换热力图显示状态
  public toggle(): Promise<void> {
    if (this.isActive) {
      this.hide();
      return Promise.resolve();
    } else {
      return this.show();
    }
  }

  // 公开方法：获取当前状态
  public getStatus(): { active: boolean; deviceCount: number; qualityLevel: string } {
    return {
      active: this.isActive,
      deviceCount: this.devicePositions.length,
      qualityLevel: this.performanceMetrics.qualityLevel,
    };
  }
}
