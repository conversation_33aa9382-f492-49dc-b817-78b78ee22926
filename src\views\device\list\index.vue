<template>
  <div class="p-4">
    <BasicTable @register="registerTable" />

    <!-- 设备预览弹窗 -->
    <Modal v-model:visible="previewModalVisible" title="设备预览" :width="800" :height="600" :destroyOnClose="true" @cancel="handleClosePreview">
      <div v-if="previewModalVisible && currentDevice">
        <DevicePreview :device="currentDevice" :key="previewKey" />
      </div>
      <template #footer>
        <div class="flex justify-end">
          <button
            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-primary rounded-md"
            @click="handleClosePreview"
          >
            关闭
          </button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script lang="ts" setup name="device-list">
  import { ref, h } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Modal } from 'ant-design-vue';
  import DevicePreview from './components/DevicePreview.vue';

  const previewModalVisible = ref(false);
  const currentDevice = ref<Recordable | null>(null);
  const previewKey = ref(0); // 用于强制重新创建组件

  // 生成假数据
  const generateMockData = () => {
    const floors = ['1F', '2F', '3F', '4F'];
    const deviceTypes = ['服务器', '配电柜', '机柜', '控制柜', '网络设备'];
    const modelPaths = ['/models/yjjg.glb', '/models/outer.glb', '/models/gaoya.glb', '/models/21.glb'];
    const mockData = [];

    for (let i = 1; i <= 20; i++) {
      const floor = floors[Math.floor(Math.random() * floors.length)];
      const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
      const modelPath = modelPaths[Math.floor(Math.random() * modelPaths.length)];

      mockData.push({
        id: i,
        modelName: `${floor}-${deviceType}-${String(i).padStart(3, '0')}`,
        floor: floor,
        deviceNumber: `DEV${String(i).padStart(4, '0')}`,
        deviceType: deviceType,
        modelPath: modelPath,
        status: '正常',
        createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      });
    }

    return mockData;
  };

  // 定义表格列
  const columns = [
    {
      title: '模型名称',
      dataIndex: 'modelName',
      width: 200,
    },
    {
      title: '楼层',
      dataIndex: 'floor',
      width: 80,
    },
    {
      title: '编号',
      dataIndex: 'deviceNumber',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'deviceType',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      customRender: ({ record }: { record: Recordable }) => {
        const status = record.status;
        const color = status === '正常' ? 'green' : 'red';
        return h('span', { style: { color } }, status);
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      fixed: true,
      align: 'center' as any,
      customRender: ({ record }: { record: Recordable }) => {
        return h('div', { class: 'flex justify-center' }, [
          h(
            'button',
            {
              class: 'inline-flex items-center text-primary cursor-pointer hover:text-blue-600',
              onClick: () => handlePreview(record),
            },
            [h('i', { class: 'i-ant-design:eye-outlined mr-1' }), '预览']
          ),
        ]);
      },
    },
  ];

  // 搜索表单配置
  const searchFormSchema = [
    {
      field: 'modelName',
      label: '模型名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'floor',
      label: '楼层',
      component: 'Select',
      componentProps: {
        options: [
          { label: '1F', value: '1F' },
          { label: '2F', value: '2F' },
          { label: '3F', value: '3F' },
          { label: '4F', value: '4F' },
          { label: '5F', value: '5F' },
          { label: '6F', value: '6F' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'deviceType',
      label: '设备类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: '服务器', value: '服务器' },
          { label: '配电柜', value: '配电柜' },
          { label: '机柜', value: '机柜' },
          { label: '控制柜', value: '控制柜' },
          { label: '网络设备', value: '网络设备' },
        ],
      },
      colProps: { span: 6 },
    },
  ];

  // 注册表格
  const [registerTable] = useTable({
    api: async (params: Recordable) => {
      // 模拟API调用
      const mockData = generateMockData();

      // 模拟搜索过滤
      let filteredData = mockData;
      if (params.modelName) {
        filteredData = filteredData.filter((item) => item.modelName.toLowerCase().includes(params.modelName.toLowerCase()));
      }
      if (params.floor) {
        filteredData = filteredData.filter((item) => item.floor === params.floor);
      }
      if (params.deviceType) {
        filteredData = filteredData.filter((item) => item.deviceType === params.deviceType);
      }

      // 模拟分页
      const pageSize = params.pageSize || 10;
      const pageNum = params.pageNum || 1;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const records = filteredData.slice(start, end);

      return {
        records,
        total: filteredData.length,
      };
    },
    columns,
    formConfig: {
      schemas: searchFormSchema as any,
      labelWidth: 80,
    },
    useSearchForm: true,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条`,
    },
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    fetchSetting: {
      pageField: 'pageNum',
      sizeField: 'pageSize',
      listField: 'records',
      totalField: 'total',
    },
  });

  // 处理预览
  const handlePreview = (record: Recordable) => {
    currentDevice.value = record;
    previewKey.value++; // 增加key强制重新创建组件
    previewModalVisible.value = true;
  };

  // 处理关闭预览
  const handleClosePreview = () => {
    previewModalVisible.value = false;
    // 延迟清理设备数据，确保组件已完全卸载
    setTimeout(() => {
      currentDevice.value = null;
    }, 300);
  };
</script>
