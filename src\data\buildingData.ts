import { ModelPaths } from '/@/api/scene';

interface Rack {
  id: string;
  name: string;
  [key: string]: any;
}

interface Floor {
  id: string;
  name: string;
  modelPath: string;
  racks: Rack[];
}

interface Building {
  id: string;
  name: string;
  modelPath: string;
  parkModelPath: string;
  floors: Floor[];
}

export const buildingData: Building = {
  id: 'building-1',
  name: '主大楼',
  modelPath: ModelPaths.ExteriorModel, // 使用合并后的外景模型
  parkModelPath: ModelPaths.ExteriorModel, // 园区和外景使用同一个模型
  floors: [
    {
      id: '1F',
      name: '1楼',
      modelPath: ModelPaths.Floor1,
      racks: [],
    },
    {
      id: '2F',
      name: '2楼',
      modelPath: ModelPaths.Floor2,
      racks: [],
    },
    {
      id: '3F',
      name: '3楼',
      modelPath: ModelPaths.Floor3,
      racks: [],
    },
    {
      id: '4F',
      name: '4楼',
      modelPath: ModelPaths.Floor4,
      racks: [],
    },
    {
      id: '5F',
      name: '5楼',
      modelPath: ModelPaths.Floor5,
      racks: [],
    },
    {
      id: '6F',
      name: '6楼',
      modelPath: ModelPaths.Floor6,
      racks: [],
    },
    {
      id: 'BF',
      name: '水泵房',
      modelPath: ModelPaths.FloorB1,
      racks: [],
    },
  ],
};
