<template>
  <div class="w-full h-full p-[0.8vw] flex flex-col gap-[0.8vw]">
    <!-- 能耗热力图 -->
    <div class="h-[20%] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">能耗热力图</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw]">
        <HeatmapPanel />
      </div>
    </div>

    <!-- 实时能耗排行榜 -->
    <div class="h-[30%] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">实时能耗排行榜</div>
        <div class="absolute right-[0.5vw] top-[0.2vw]">
          <button
            @click="showFullRankingChart"
            class="w-[1.2vw] h-[1.2vw] bg-blue-500 hover:bg-blue-600 rounded text-white text-[0.5vw] flex items-center justify-center transition-colors"
            title="查看完整排行图表"
          >
            <svg class="w-[0.6vw] h-[0.6vw]" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
            </svg>
          </button>
        </div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-hidden">
        <ConsumptionRankingCarousel ref="rankingCarouselRef" />
      </div>
    </div>

    <!-- 楼层电耗 -->
    <div class="h-[30%] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">楼层电耗</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto">
        <FloorElectricityPanel />
      </div>
    </div>

    <!-- 设备实时监控 -->
    <div class="h-[20%] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">设备实时监控</div>
      </div>
      <div class="flex-1 mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw] overflow-auto">
        <DeviceMonitorPanel />
      </div>
    </div>

    <!-- 弹窗 -->
    <ModalDialog v-model:visible="rankingModalVisible" title="完整实时能耗排行榜" width="80vw" height="70vh">
      <div class="w-full h-full">
        <div class="mb-4 flex justify-center">
          <div class="flex bg-[#15274D]/70 rounded p-1">
            <button
              v-for="(title, index) in rankingTitles"
              :key="index"
              @click="activeRankingTab = index"
              :class="[
                'px-4 py-2 rounded text-sm transition-all',
                activeRankingTab === index ? 'bg-blue-600 text-white shadow-md' : 'text-blue-100 bg-[#22335a] hover:text-white hover:bg-blue-500/60',
              ]"
            >
              {{ title }}
            </button>
          </div>
        </div>
        <div class="h-[calc(100%-4rem)]">
          <ElectricityFullRankingPanel v-if="activeRankingTab === 0" />
          <WaterFullRankingPanel v-if="activeRankingTab === 1" />
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import HeatmapPanel from './components/HeatmapPanel.vue';
  import DeviceMonitorPanel from './components/DeviceMonitorPanel.vue';
  import ConsumptionRankingCarousel from './components/ConsumptionRankingCarousel.vue';
  import FloorElectricityPanel from './components/FloorElectricityPanel.vue';
  import ModalDialog from '/src/views/scene/components/ModalDialog.vue';
  import ElectricityFullRankingPanel from './components/ElectricityFullRankingPanel.vue';
  import WaterFullRankingPanel from './components/WaterFullRankingPanel.vue';

  const rankingCarouselRef = ref();
  const rankingModalVisible = ref(false);
  const activeRankingTab = ref(0);
  const rankingTitles = ['电耗实时排行榜', '水耗实时排行榜'];

  const showFullRankingChart = () => {
    activeRankingTab.value = rankingCarouselRef.value?.currentIndex || 0;
    rankingModalVisible.value = true;
  };
</script>
