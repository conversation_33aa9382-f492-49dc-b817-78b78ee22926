<template>
  <div class="h-full flex flex-col gap-[0.8vw]">
    <!-- 标签切换 -->
    <div class="bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
      <div class="flex gap-[0.4vw]">
        <button
          class="px-[0.8vw] py-[0.4vw] text-[0.7vw] rounded transition-colors"
          :class="currentTab === 'device' ? 'bg-blue-500 text-white' : 'bg-[#15274D]/60 text-white/70 hover:bg-[#15274D]/80'"
          @click="currentTab = 'device'"
        >
          设备管理
        </button>
        <button
          class="px-[0.8vw] py-[0.4vw] text-[0.7vw] rounded transition-colors"
          :class="currentTab === 'cabinet' ? 'bg-blue-500 text-white' : 'bg-[#15274D]/60 text-white/70 hover:bg-[#15274D]/80'"
          @click="currentTab = 'cabinet'"
        >
          机柜管理
        </button>
      </div>
    </div>

    <!-- 列表区域 -->
    <div class="flex-1 bg-[#15274D]/40 backdrop-blur-sm rounded p-[0.8vw]">
      <div class="text-[0.7vw] text-white mb-[0.6vw] flex items-center justify-between">
        <div class="flex items-center">
          <component :is="currentTab === 'device' ? 'DesktopOutlined' : 'BoxPlotOutlined'" class="mr-[0.4vw] text-blue-400" />
          {{ currentTab === 'device' ? '设备列表' : '机柜列表' }}
          <LoadingOutlined v-if="loading" class="ml-[0.4vw] text-blue-400 animate-spin" />
        </div>
        <div class="text-[0.6vw] text-gray-400" v-if="filteredAssets.length > 0"> 共 {{ filteredAssets.length }} 项 </div>
      </div>

      <!-- 列表内容 -->
      <div
        class="space-y-[0.4vw] overflow-y-auto h-[calc(100%-2.5vw)] scrollbar scrollbar-rounded scrollbar-track-transparent scrollbar-thumb-white/30 pr-[0.4vw]"
      >
        <div v-if="loading" class="h-full flex items-center justify-center py-[2vw]">
          <LoadingOutlined class="text-[1.5vw] text-blue-400 animate-spin" />
          <span class="ml-[0.6vw] text-[0.7vw] text-white">加载中...</span>
        </div>
        <div v-else-if="filteredAssets.length === 0" class="h-full flex items-center justify-center py-[2vw]">
          <span class="text-[0.7vw] text-gray-400">暂无{{ currentTab === 'device' ? '设备' : '机柜' }}数据</span>
        </div>
        <div
          v-else
          v-for="item in filteredAssets"
          :key="item.id"
          class="rounded-lg p-[0.7vw] bg-black/25 border border-white/10 hover:bg-black/35 hover:border-white/20 transition-all duration-200"
        >
          <div class="flex items-center justify-between">
            <div class="text-[0.75vw] text-white flex items-center">
              <component :is="currentTab === 'device' ? 'DesktopOutlined' : 'BoxPlotOutlined'" class="mr-[0.4vw] text-[0.9vw] text-blue-400" />
              {{ item.name }}
            </div>
            <button
              class="px-[0.4vw] py-[0.2vw] bg-blue-500 text-white text-[0.6vw] rounded hover:bg-blue-600 transition-all duration-200 flex items-center shadow-sm hover:shadow-md"
              :class="{ 'opacity-50 cursor-not-allowed': !item.modelObject }"
              @click="focusOnAsset(item)"
              :title="item.modelObject ? '定位到设备' : '未找到对应的3D模型'"
            >
              <EyeOutlined class="mr-[0.15vw] text-[0.65vw]" />
              定位
            </button>
          </div>
          <div class="mt-[0.4vw] text-[0.65vw] text-gray-400">
            <div>模型名称: {{ item.modelName || '-' }}</div>
            <div>所在机房: {{ item.roomName || '-' }}</div>
            <div v-if="currentTab === 'device'">设备编码: {{ item.code || '-' }}</div>
            <div v-if="currentTab === 'cabinet'">楼层: {{ item.floors || '-' }}</div>
            <div class="text-[0.6vw]" :class="item.modelObject ? 'text-green-400' : 'text-red-400'">
              {{ item.modelObject ? '✓ 已关联3D模型' : '✗ 未找到3D模型' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { DesktopOutlined, BoxPlotOutlined, LoadingOutlined, EyeOutlined } from '@ant-design/icons-vue';
  import { ModelLoaderManager } from '@/views/scene/lib/load/ModelLoaderManager';
  import { CameraController } from '@/views/scene/lib/CameraController';
  import { ObjectSelection } from '@/views/scene/lib/selection/ObjectSelection';
  import { isFloorDevice, extractFloorNumber } from '@/views/scene/utils/deviceIdentifier';
  import { getAssetDeviceListSimple } from '/@/api/asset/device';
  import { getAssetCabinetListSimple } from '/@/api/asset/cabinet';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
  import * as THREE from 'three';

  const props = defineProps({
    currentFloor: {
      type: String,
      required: true,
    },
  });

  // 状态
  const loading = ref(false);
  const currentTab = ref('device');
  const assets = ref([]);
  const deviceModelMap = ref(new Map()); // 存储设备ID和3D模型的映射关系

  // API 请求函数
  const getDeviceList = async () => {
    return await getAssetDeviceListSimple();
  };

  const getCabinetList = async () => {
    return await getAssetCabinetListSimple();
  };

  // 根据当前选项卡过滤资产
  const filteredAssets = computed(() => {
    return currentTab.value === 'device'
      ? assets.value.filter((asset) => asset.type !== 'cabinet')
      : assets.value.filter((asset) => asset.type === 'cabinet');
  });
  // 从3D模型中通过设备编码查找设备对象
  const findDeviceByCode = (deviceCode) => {
    if (!deviceCode) return null;

    try {
      const modelLoader = ModelLoaderManager.getInstance();
      const models = modelLoader.getCurrentModels();
      const currentFloorNumber = props.currentFloor;

      let foundDevice = null;
      let bestMatch = null;
      let matchScore = 0;

      models.forEach((model) => {
        if (foundDevice) return; // 已找到完全匹配，跳过后续遍历

        model.traverse((object) => {
          if (!object.name) return;

          const objectNameLower = object.name.toLowerCase();
          const deviceCodeLower = deviceCode.toLowerCase();

          // 方案1：优先查找符合楼层设备规范的模型
          if (isFloorDevice(object.name)) {
            const deviceFloor = extractFloorNumber(object.name);

            // 检查楼层是否匹配
            if (deviceFloor && deviceFloor === currentFloorNumber.toString()) {
              // 检查设备名称是否包含设备编码
              if (objectNameLower === deviceCodeLower) {
                foundDevice = object;
                console.log(`[AssetManager] 找到完全匹配的楼层设备: ${deviceCode} -> ${object.name}`);
                return;
              } else if (objectNameLower.includes(deviceCodeLower) || deviceCodeLower.includes(objectNameLower)) {
                if (matchScore < 3) {
                  bestMatch = object;
                  matchScore = 3;
                  console.log(`[AssetManager] 找到部分匹配的楼层设备: ${deviceCode} -> ${object.name}`);
                }
              }
            }
          }

          // 方案2：如果楼层设备规范查找失败，则进行宽松查找（不限制楼层格式）
          if (!foundDevice) {
            // 完全匹配（最高优先级）
            if (objectNameLower === deviceCodeLower) {
              if (matchScore < 5) {
                bestMatch = object;
                matchScore = 5;
                console.log(`[AssetManager] 找到完全匹配的设备: ${deviceCode} -> ${object.name}`);
              }
            }
            // 包含匹配（中等优先级）
            else if (objectNameLower.includes(deviceCodeLower)) {
              if (matchScore < 2) {
                bestMatch = object;
                matchScore = 2;
                console.log(`[AssetManager] 找到包含匹配的设备: ${deviceCode} -> ${object.name}`);
              }
            }
            // 反向包含匹配（较低优先级）
            else if (deviceCodeLower.includes(objectNameLower) && objectNameLower.length > 3) {
              if (matchScore < 1) {
                bestMatch = object;
                matchScore = 1;
                console.log(`[AssetManager] 找到反向匹配的设备: ${deviceCode} -> ${object.name}`);
              }
            }
          }
        });
      });

      // 如果没有找到完全匹配，使用最佳匹配
      if (!foundDevice && bestMatch) {
        foundDevice = bestMatch;
        console.log(`[AssetManager] 使用最佳匹配设备: ${deviceCode} -> ${foundDevice.name} (匹配分数: ${matchScore})`);
      }

      if (!foundDevice) {
        console.warn(`[AssetManager] 未找到设备编码 "${deviceCode}" 对应的3D模型`);

        // 调试信息：列出当前楼层的所有模型名称
        if (process.env.NODE_ENV === 'development') {
          const allNames = [];
          models.forEach((model) => {
            model.traverse((object) => {
              if (object.name && object.name.trim()) {
                allNames.push(object.name);
              }
            });
          });
          console.debug(`[AssetManager] 当前场景中的所有模型名称:`, allNames.slice(0, 20)); // 只显示前20个
        }
      }

      return foundDevice;
    } catch (error) {
      console.error(`[AssetManager] 查找设备 "${deviceCode}" 失败:`, error);
      return null;
    }
  };
  // 从3D模型中获取设备对象并建立映射关系
  const buildDeviceModelMap = () => {
    try {
      const modelLoader = ModelLoaderManager.getInstance();
      const models = modelLoader.getCurrentModels();
      const devices = new Map();
      const processedIds = new Set();

      const currentFloorNumber = props.currentFloor;

      models.forEach((model) => {
        model.traverse((object) => {
          if (processedIds.has(object.uuid) || !object.name) return;
          processedIds.add(object.uuid);

          // 优先匹配符合楼层设备规范的模型
          if (isFloorDevice(object.name)) {
            const deviceFloor = extractFloorNumber(object.name);
            if (deviceFloor && deviceFloor === currentFloorNumber.toString()) {
              devices.set(object.name, object);
            }
          }
          // 如果对象名称包含楼层信息但不符合严格规范，也尝试包含
          else if (object.name.match(/\d+f|\df|floor|楼/i)) {
            devices.set(object.name, object);
          }
          // 其他可能的设备对象也包含进来
          else if (object.name.match(/device|设备|机柜|cabinet|server|ups|pdu|ac|air|sensor|传感器/i)) {
            devices.set(object.name, object);
          }
        });
      });

      deviceModelMap.value = devices;
      console.log(`[AssetManager] 建立设备映射关系，共 ${devices.size} 个设备`);

      // 调试信息：列出所有找到的设备名称
      if (process.env.NODE_ENV === 'development') {
        const deviceNames = Array.from(devices.keys()).slice(0, 10); // 只显示前10个
        console.debug(`[AssetManager] 找到的设备名称示例:`, deviceNames);
      }

      return devices;
    } catch (error) {
      console.error('[AssetManager] 从3D模型获取设备失败:', error);
      return new Map();
    }
  };

  // 加载设备数据
  const loadDevices = async () => {
    try {
      loading.value = true;

      // 构建3D模型映射
      buildDeviceModelMap();

      // 根据当前标签页加载不同的数据
      const [deviceRes, cabinetRes] = await Promise.all([getDeviceList(), getCabinetList()]);

      let combinedAssets = [];
      if (deviceRes) {
        const devices = deviceRes.map((device) => {
          // 通过设备编码查找对应的3D模型
          const modelObject = findDeviceByCode(device.code);

          return {
            id: device.id,
            name: device.deviceName,
            type: 'device',
            modelName: modelObject ? modelObject.name : device.code, // 如果找到3D模型则使用模型名称，否则使用设备编码
            roomName: device.roomName,
            code: device.code,
            floors: device.floors,
            modelObject: modelObject, // 使用查找到的3D模型对象
          };
        });
        combinedAssets = [...combinedAssets, ...devices];
      }

      if (cabinetRes) {
        const cabinets = cabinetRes.map((cabinet) => {
          // 通过机柜名称查找对应的3D模型
          const modelObject = findDeviceByCode(cabinet.name);

          return {
            id: cabinet.id,
            name: cabinet.name,
            type: 'cabinet',
            modelName: modelObject ? modelObject.name : cabinet.name, // 如果找到3D模型则使用模型名称，否则使用机柜名称
            roomName: cabinet.roomName,
            floors: cabinet.floors,
            modelObject: modelObject, // 使用查找到的3D模型对象
          };
        });
        combinedAssets = [...combinedAssets, ...cabinets];
      }

      // 只保留当前楼层的设备
      assets.value = combinedAssets.filter((asset) => {
        const floorNumber = asset.floors ? asset.floors.toString() : null;
        return floorNumber === props.currentFloor;
      });

      console.log(
        `[AssetManager] 加载完成，共 ${assets.value.length} 个资产，其中 ${assets.value.filter((a) => a.modelObject).length} 个已关联3D模型`
      );
    } catch (error) {
      console.error('[AssetManager] 加载设备数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 定位到设备 - 重新设计的简化版本
  const focusOnAsset = async (asset) => {
    try {
      console.log(`[AssetManager] 开始定位设备: ${asset.name} (${asset.code})`);

      // 1. 从设备code中提取楼层信息（格式：F1_A102_KTG2）
      let deviceFloor = null;
      if (asset.code && asset.code.match(/^F(\d+)_/)) {
        deviceFloor = asset.code.match(/^F(\d+)_/)[1];
      }

      if (!deviceFloor) {
        console.warn(`[AssetManager] 无法从设备code "${asset.code}" 中提取楼层信息`);
        if (window.$message) {
          window.$message.warning('无法识别设备楼层，请检查设备编码格式');
        }
        return;
      }

      console.log(`[AssetManager] 设备楼层: ${deviceFloor}`);

      // 2. 检查当前楼层，如果不匹配则切换楼层
      const globalThreeStore = useGlobalThreeStore();
      const currentFloorId = globalThreeStore.currentFloorId;
      const targetFloorId = `${deviceFloor}F`;

      if (currentFloorId !== targetFloorId) {
        console.log(`[AssetManager] 需要从 ${currentFloorId} 切换到 ${targetFloorId}`);

        if (window.$message) {
          window.$message.info(`正在切换到${deviceFloor}楼...`);
        }

        try {
          await globalThreeStore.switchFloor(targetFloorId);
          // 等待楼层切换完成
          await new Promise((resolve) => setTimeout(resolve, 800));
          console.log(`[AssetManager] 楼层切换完成`);
        } catch (error) {
          console.error('[AssetManager] 楼层切换失败:', error);
          if (window.$message) {
            window.$message.error(`切换到${deviceFloor}楼失败`);
          }
          return;
        }
      }

      // 3. 在当前楼层查找设备对象
      const modelLoader = ModelLoaderManager.getInstance();
      let deviceObject = null;

      console.log(`[AssetManager] 开始查找设备对象，目标code: ${asset.code}`);

      // 方法1：直接通过ModelLoaderManager查找
      deviceObject = modelLoader.findObjectByName(asset.code);
      if (deviceObject) {
        console.log(`[AssetManager] 方法1找到设备: ${deviceObject.name}`);
      }

      // 方法2：如果没找到，遍历场景查找所有楼层设备并打印
      if (!deviceObject) {
        console.log(`[AssetManager] 方法1未找到，开始遍历场景查找设备...`);
        const models = modelLoader.getCurrentModels();
        const allDevices = [];

        for (const model of models) {
          model.traverse((object) => {
            if (object.name && isFloorDevice(object.name)) {
              allDevices.push(object.name);

              // 检查是否匹配目标设备
              if (!deviceObject && (object.name === asset.code || object.name.includes(asset.code) || asset.code.includes(object.name))) {
                deviceObject = object;
                console.log(`[AssetManager] 找到匹配设备: ${object.name}`);
              }
            }
          });
          if (deviceObject) break;
        }

        console.log(`[AssetManager] 当前楼层所有设备列表:`, allDevices.slice(0, 10)); // 只显示前10个避免日志过多
        console.log(`[AssetManager] 总设备数量: ${allDevices.length}`);
      }

      if (!deviceObject) {
        console.warn(`[AssetManager] 未找到设备对象: ${asset.code}`);
        if (window.$message) {
          window.$message.warning(`未找到设备 "${asset.name}" 的3D模型`);
        }
        return;
      }

      // 4. 执行设备聚焦 - 参考双击设备的逻辑
      const objectSelection = ObjectSelection.getInstance();
      if (objectSelection?.objectDoubleClickHandler) {
        console.log(`[AssetManager] 开始聚焦设备: ${deviceObject.name}`);

        // 直接调用双击处理器的设备聚焦功能
        await objectSelection.objectDoubleClickHandler.handleObjectDoubleSelection(deviceObject);

        if (window.$message) {
          window.$message.success(`已定位到设备: ${asset.name}（${deviceFloor}楼）`);
        }

        console.log(`[AssetManager] 设备定位完成: ${asset.name}`);
      } else {
        console.error('[AssetManager] 无法获取ObjectSelection实例');
        if (window.$message) {
          window.$message.error('定位功能初始化失败');
        }
      }
    } catch (error) {
      console.error('[AssetManager] 定位设备失败:', error);
      if (window.$message) {
        window.$message.error(`定位设备失败: ${error.message}`);
      }
    }
  };

  // 监听标签页切换
  watch(currentTab, () => {
    if (assets.value.length > 0) {
      // 只有在已有数据的情况下，切换标签页才重新加载
      loadDevices();
    }
  });

  // 监听当前楼层变化
  watch(
    () => props.currentFloor,
    () => {
      loadDevices();
    }
  );

  // 组件挂载时加载数据
  onMounted(() => {
    loadDevices();
  });
</script>

<style lang="less" scoped>
  .scrollbar {
    &::-webkit-scrollbar {
      width: 0.4vw;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 0.2vw;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 0.2vw;

      &:hover {
        background-color: rgba(255, 255, 255, 0.4);
      }
    }
  }
</style>
