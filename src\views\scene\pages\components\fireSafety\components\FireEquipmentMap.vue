<template>
  <div class="h-[14vw] bg-[#15274D]/40 backdrop-blur-sm rounded overflow-hidden relative">
    <div class="flex flex-col">
      <!-- 标题栏 -->
      <div class="h-[1.6vw] shrink-0 relative flex items-center">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="text-white absolute left-[1vw] top-1/2 -translate-y-1/2 text-[0.8vw] font-medium tracking-wider">消防设备定位</div>
      </div>
      <!-- 控制栏 -->
      <div class="h-[1.8vw] px-[0.8vw] flex items-center bg-[#1E2A47]/10">
        <div class="text-[0.65vw] text-white">当前楼层</div>
        <div class="flex-1 flex items-center justify-end">
          <div class="z-10 w-[5vw]">
            <MiniSelect
              v-model="selectedFloor"
              :options="
                floors.map((floor) => ({
                  label: floor.name,
                  value: floor.id,
                }))
              "
            />
          </div>
        </div>
      </div>
    </div>

    <div class="p-[0.8vw]">
      <div class="device-map-container h-[8vw] rounded overflow-hidden relative bg-[#0C1526]/80">
        <!-- 楼层平面图 -->
        <div class="w-full h-full relative">
          <img :src="getFloorMap" class="w-full h-full object-contain opacity-80" alt="楼层平面图" />

          <!-- 设备标记点 -->
          <div
            v-for="device in filteredDevices"
            :key="device.id"
            class="absolute device-marker flex flex-col items-center"
            :class="device.status === 'alert' ? 'marker-alert' : ''"
            :style="{
              left: `${device.position.x}%`,
              top: `${device.position.y}%`,
            }"
            @click="selectDevice(device)"
          >
            <div
              class="w-[0.8vw] h-[0.8vw] rounded-full border-2 border-white flex items-center justify-center"
              :class="getDeviceMarkerClass(device)"
            >
              <component :is="device.icon" class="text-[0.5vw]" />
            </div>
            <div
              v-if="selectedDeviceId === device.id"
              class="text-[0.6vw] text-white bg-black/60 px-[0.3vw] py-[0.1vw] rounded mt-[0.2vw] whitespace-nowrap"
            >
              {{ device.name }}
            </div>
          </div>
        </div>

        <!-- 图层控制按钮 -->
        <div class="absolute top-[0.5vw] right-[0.5vw] flex flex-col gap-[0.3vw]">
          <a-button
            type="primary"
            size="small"
            :class="activeDeviceTypes.includes('smoke') ? '!bg-blue-600' : '!bg-gray-600'"
            @click="toggleDeviceType('smoke')"
          >
            <template #icon><FireOutlined /></template>
            烟感
          </a-button>
          <a-button
            type="primary"
            size="small"
            :class="activeDeviceTypes.includes('water') ? '!bg-blue-600' : '!bg-gray-600'"
            @click="toggleDeviceType('water')"
          >
            <template #icon><SafetyCertificateOutlined /></template>
            消防栓
          </a-button>
          <a-button
            type="primary"
            size="small"
            :class="activeDeviceTypes.includes('camera') ? '!bg-blue-600' : '!bg-gray-600'"
            @click="toggleDeviceType('camera')"
          >
            <template #icon><VideoCameraOutlined /></template>
            摄像头
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import MiniSelect from '@/views/scene/components/MiniSelect.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import { message } from 'ant-design-vue';
  import { FireOutlined, SafetyCertificateOutlined, VideoCameraOutlined } from '@ant-design/icons-vue';

  // 楼层列表
  const floors = ref([
    { id: 1, name: '1层', mapImg: 'floor1.png' },
    { id: 2, name: '2层', mapImg: 'floor2.png' },
    { id: 3, name: '3层', mapImg: 'floor3.png' },
    { id: 4, name: '4层', mapImg: 'floor4.png' },
    { id: 5, name: '5层', mapImg: 'floor5.png' },
    { id: 6, name: '6层', mapImg: 'floor6.png' },
  ]);
  const selectedFloor = ref(1);

  // 设备类型过滤
  const activeDeviceTypes = ref(['smoke', 'water', 'camera']);

  // 设备列表
  const devices = ref([
    { id: 1, name: '烟感01', type: 'smoke', status: 'normal', icon: FireOutlined, position: { x: 20, y: 30 }, floor: 1 },
    { id: 2, name: '烟感02', type: 'smoke', status: 'alert', icon: FireOutlined, position: { x: 40, y: 20 }, floor: 1 },
    { id: 3, name: '烟感03', type: 'smoke', status: 'normal', icon: FireOutlined, position: { x: 60, y: 50 }, floor: 1 },
    { id: 4, name: '消防栓01', type: 'water', status: 'normal', icon: SafetyCertificateOutlined, position: { x: 25, y: 60 }, floor: 1 },
    { id: 5, name: '消防栓02', type: 'water', status: 'normal', icon: SafetyCertificateOutlined, position: { x: 70, y: 40 }, floor: 1 },
    { id: 6, name: '摄像头01', type: 'camera', status: 'normal', icon: VideoCameraOutlined, position: { x: 35, y: 25 }, floor: 1 },
    { id: 7, name: '摄像头02', type: 'camera', status: 'offline', icon: VideoCameraOutlined, position: { x: 55, y: 35 }, floor: 1 },
    // 2层设备
    { id: 8, name: '烟感04', type: 'smoke', status: 'normal', icon: FireOutlined, position: { x: 30, y: 40 }, floor: 2 },
    { id: 9, name: '消防栓03', type: 'water', status: 'normal', icon: SafetyCertificateOutlined, position: { x: 50, y: 30 }, floor: 2 },
    { id: 10, name: '摄像头03', type: 'camera', status: 'normal', icon: VideoCameraOutlined, position: { x: 65, y: 45 }, floor: 2 },
  ]);

  const selectedDeviceId = ref(null);

  // 过滤当前显示的设备
  const filteredDevices = computed(() => {
    return devices.value.filter((device) => device.floor === selectedFloor.value && activeDeviceTypes.value.includes(device.type));
  });

  // 获取楼层地图
  const getFloorMap = computed(() => {
    const floor = floors.value.find((f) => f.id === selectedFloor.value);
    // 返回地图URL，这里可以替换成实际的地图资源
    return `https://via.placeholder.com/800x600?text=Floor+${floor.id}`;
  });

  // 根据设备状态获取样式
  const getDeviceMarkerClass = (device) => {
    switch (device.status) {
      case 'alert':
        return 'bg-red-500 animate-pulse';
      case 'offline':
        return 'bg-gray-500';
      default:
        return 'bg-green-500';
    }
  };

  // 切换设备类型显示
  const toggleDeviceType = (type) => {
    if (activeDeviceTypes.value.includes(type)) {
      if (activeDeviceTypes.value.length > 1) {
        // 确保至少选中一种类型
        activeDeviceTypes.value = activeDeviceTypes.value.filter((t) => t !== type);
      }
    } else {
      activeDeviceTypes.value.push(type);
    }
  };

  // 选择设备
  const selectDevice = (device) => {
    selectedDeviceId.value = selectedDeviceId.value === device.id ? null : device.id;
    if (selectedDeviceId.value) {
      message.info(`已选择${device.name}`);
    }
  };
</script>

<style scoped>
  .device-map-container {
    background-image: linear-gradient(to right bottom, rgba(12, 21, 38, 0.7), rgba(12, 21, 38, 0.8)),
      url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%23246CF9" stroke-width="0.5" stroke-dasharray="5,5" /></svg>');
    background-size: cover;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .device-marker {
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    z-index: 5;
  }

  .marker-alert {
    z-index: 10;
  }

  .device-marker:hover {
    transform: translate(-50%, -50%) scale(1.2);
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.5);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
</style>
