<template>
  <div class="h-[38%] relative flex flex-col">
    <div class="h-[1.6vw] shrink-0 relative">
      <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
      <div class="absolute left-[1vw] top-[0.25vw] text-white text-[0.7vw]">疫情防控热力图</div>
    </div>
    <div class="flex-1 flex flex-col h-[calc(100%-2vw)] mt-[0.4vw] bg-[#15274D]/30 rounded p-[0.6vw]">
      <!-- 顶部控制区域 -->
      <div class="flex justify-center items-center mb-[0.6vw] relative">
        <!-- 楼层切换按钮 -->
        <div class="flex gap-[0.4vw]">
          <div
            v-for="floor in ['6F', '5F', '4F', '3F', '2F', '1F']"
            :key="floor"
            class="w-[3vw] h-[1.6vw] flex items-center justify-center rounded cursor-pointer transition-all text-[0.6vw]"
            :class="currentFloor === floor ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-white/60 hover:bg-[#3B8EE6]/20'"
            @click="changeFloor(floor)"
          >
            {{ floor }}
          </div>
        </div>
        <!-- 添加放大按钮，放在楼层选择后面 -->
        <div
          class="w-[1.6vw] h-[1.6vw] ml-[0.4vw] flex items-center justify-center rounded cursor-pointer bg-black/20 hover:bg-[#3B8EE6]/20 transition-all"
          @click="showFullscreen"
        >
          <FullscreenOutlined class="text-white/80 text-[0.8vw]" />
        </div>
      </div>

      <!-- 热力图主体区域 -->
      <div class="relative flex-1 bg-[#15274D]/50 rounded-lg overflow-hidden">
        <div ref="heatmapChart" class="w-full h-full"></div>
      </div>
    </div>
  </div>

  <!-- 全屏弹窗 -->
  <ModalDialog v-model:visible="dialogVisible" title="疫情防控热力图" width="80vw" height="80vh" :show-footer="false" :iconSrc="dashboardTitle">
    <div class="w-full h-full bg-[#15274D]/50 rounded-lg p-[1vw] relative flex flex-col">
      <!-- 楼层切换按钮组 - 弹窗中也使用上下布局 -->
      <div class="flex justify-center mb-[1vw]">
        <div
          v-for="floor in ['6F', '5F', '4F', '3F', '2F', '1F']"
          :key="floor"
          class="mx-[0.6vw] w-[4vw] h-[2vw] flex items-center justify-center rounded cursor-pointer text-[0.8vw] transition-all"
          :class="currentFloor === floor ? 'bg-[#3B8EE6] text-white' : 'bg-black/20 text-white/60 hover:bg-[#3B8EE6]/20'"
          @click="changeFloor(floor)"
        >
          {{ floor }}
        </div>
      </div>

      <!-- 大尺寸热力图 -->
      <div class="flex-1">
        <div ref="fullscreenChart" class="w-full h-full"></div>
      </div>
    </div>
  </ModalDialog>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
  import { FullscreenOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts/core';
  import { GridComponent } from 'echarts/components';
  import { HeatmapChart } from 'echarts/charts';
  import { TooltipComponent, VisualMapComponent, TitleComponent, LegendComponent } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';
  import ModalDialog from '../../../../components/ModalDialog.vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';

  // 注册必需的组件
  echarts.use([GridComponent, HeatmapChart, TooltipComponent, VisualMapComponent, TitleComponent, LegendComponent, CanvasRenderer]);

  const heatmapChart = ref(null);
  const fullscreenChart = ref(null);
  let heatmapInstance = null;
  let fullscreenChartInstance = null;
  const currentFloor = ref('1F');
  const dialogVisible = ref(false);

  // 楼层数据映射
  const floorData = {
    '1F': { xCategories: ['入口', '大厅', '办公区', '走廊', '休息区'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
    '2F': { xCategories: ['会议室', '办公区', '电梯口', '茶水间', '走廊'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
    '3F': { xCategories: ['机房', '办公区', '控制室', '休息区', '走廊'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
    '4F': { xCategories: ['培训室', '办公区', '小会议室', '大会议室', '走廊'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
    '5F': { xCategories: ['数据中心', '办公区', '监控室', '设备间', '走廊'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
    '6F': { xCategories: ['顶层办公', '会议区', '休闲区', '天台入口', '走廊'], yCategories: ['A区', 'B区', 'C区', 'D区'] },
  };

  // 生成热力图数据
  const generateHeatmapData = (floor) => {
    try {
      const floorConfig = floorData[floor];
      if (!floorConfig) return [];

      const xCount = floorConfig.xCategories.length;
      const yCount = floorConfig.yCategories.length;

      // 生成数据点
      const data = [];
      for (let x = 0; x < xCount; x++) {
        for (let y = 0; y < yCount; y++) {
          // 不同楼层有不同的基础人流量
          const baseValue =
            {
              '1F': 70, // 一楼基础人流量高
              '2F': 50,
              '3F': 30,
              '4F': 40,
            }[floor] || 50; // 默认值防止undefined

          // 中心区域人流量相对较高
          const centerFactorX = 1 - Math.abs((x - (xCount - 1) / 2) / ((xCount - 1) / 2 || 1)); // 防止除0
          const centerFactorY = 1 - Math.abs((y - (yCount - 1) / 2) / ((yCount - 1) / 2 || 1)); // 防止除0
          const centerBonus = (centerFactorX + centerFactorY) * 15;

          // 随机波动
          const randomFactor = Math.random() * 20 - 10;

          // 计算最终值
          let value = Math.round(baseValue + centerBonus + randomFactor);
          value = Math.max(0, Math.min(100, value)); // 限制在0-100范围内

          data.push([x, y, value]);
        }
      }

      return data;
    } catch (error) {
      console.error('生成热力图数据出错:', error);
      return [];
    }
  };

  // 初始化热力图
  const initHeatmap = (container, isFullscreen = false, floor = '1F') => {
    try {
      if (!container) return null;

      const floorConfig = floorData[floor];
      if (!floorConfig) return null;

      const chartInstance = echarts.init(container);
      const option = {
        tooltip: {
          position: 'top',
          formatter: function (params) {
            return `${floorConfig.xCategories[params.data[0]]}, ${floorConfig.yCategories[params.data[1]]}<br>人流密度: ${params.data[2]}`;
          },
          confine: true,
          appendToBody: true,
          extraCssText:
            'background: rgba(0,0,0,0.7); border-radius: 4px; border: none; box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); color: #fff; font-size: 12px;',
          textStyle: {
            color: '#fff',
            fontSize: isFullscreen ? 14 : 12,
          },
        },
        grid: {
          top: '10%',
          left: '5%',
          right: '18%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: floorConfig.xCategories,
          splitArea: { show: true },
          axisLine: { lineStyle: { color: 'rgba(255,255,255,0.3)' } },
          axisLabel: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: isFullscreen ? 14 : 10,
          },
        },
        yAxis: {
          type: 'category',
          data: floorConfig.yCategories,
          splitArea: { show: true },
          axisLine: { lineStyle: { color: 'rgba(255,255,255,0.3)' } },
          axisLabel: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: isFullscreen ? 14 : 10,
          },
        },
        visualMap: {
          min: 0,
          max: 100,
          calculable: true,
          orient: 'vertical',
          right: '0%',
          top: 'center',
          inRange: {
            color: ['#313695', '#4575B4', '#74ADD1', '#F46D43', '#D73027'],
          },
          textStyle: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: isFullscreen ? 12 : 10,
          },
          formatter: function (value) {
            return Math.round(value);
          },
        },
        series: [
          {
            name: '人流密度',
            type: 'heatmap',
            data: generateHeatmapData(floor),
            label: {
              show: true,
              color: '#fff',
              fontSize: isFullscreen ? 14 : 10,
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };

      chartInstance.setOption(option);
      return chartInstance;
    } catch (error) {
      console.error('初始化热力图出错:', error);
      return null;
    }
  };

  // 显示全屏弹窗
  const showFullscreen = () => {
    dialogVisible.value = true;
  };

  // 更新图表数据
  const updateChartData = (floor) => {
    try {
      if (!floorData[floor]) return;
      const floorConfig = floorData[floor];

      if (heatmapInstance) {
        heatmapInstance.setOption({
          xAxis: {
            data: floorConfig.xCategories,
          },
          yAxis: {
            data: floorConfig.yCategories,
          },
          series: [
            {
              data: generateHeatmapData(floor),
            },
          ],
        });
      }

      if (fullscreenChartInstance && dialogVisible.value) {
        fullscreenChartInstance.setOption({
          xAxis: {
            data: floorConfig.xCategories,
          },
          yAxis: {
            data: floorConfig.yCategories,
          },
          series: [
            {
              data: generateHeatmapData(floor),
            },
          ],
        });
      }
    } catch (error) {
      console.error('更新图表数据出错:', error);
    }
  };

  // 切换楼层
  const changeFloor = (floor) => {
    currentFloor.value = floor;
    updateChartData(floor);
  };

  // 监听弹窗状态
  watch(
    () => dialogVisible.value,
    (newVal) => {
      if (newVal) {
        setTimeout(() => {
          try {
            if (fullscreenChart.value) {
              fullscreenChartInstance = initHeatmap(fullscreenChart.value, true, currentFloor.value);
            }
          } catch (error) {
            console.error('初始化全屏图表出错:', error);
          }
        }, 100);
      } else {
        if (fullscreenChartInstance) {
          try {
            fullscreenChartInstance.dispose();
          } catch (error) {
            console.error('销毁全屏图表出错:', error);
          }
          fullscreenChartInstance = null;
        }
      }
    }
  );

  // 生命周期钩子
  onMounted(() => {
    try {
      // 延迟初始化，确保DOM已渲染
      setTimeout(() => {
        if (heatmapChart.value) {
          heatmapInstance = initHeatmap(heatmapChart.value, false, currentFloor.value);
        }
      }, 100);
    } catch (error) {
      console.error('组件挂载时初始化图表出错:', error);
    }
  });

  onBeforeUnmount(() => {
    try {
      if (heatmapInstance) {
        heatmapInstance.dispose();
      }
    } catch (error) {
      console.error('销毁热力图实例出错:', error);
    }

    try {
      if (fullscreenChartInstance) {
        fullscreenChartInstance.dispose();
      }
    } catch (error) {
      console.error('销毁全屏图表实例出错:', error);
    }

    heatmapInstance = null;
    fullscreenChartInstance = null;
  });

  // 暴露给父组件的方法
  defineExpose({
    resize: () => {
      try {
        if (heatmapInstance) {
          heatmapInstance.resize();
        }
        if (fullscreenChartInstance) {
          fullscreenChartInstance.resize();
        }
      } catch (error) {
        console.error('调整图表大小出错:', error);
      }
    },
    dispose: () => {
      try {
        if (heatmapInstance) {
          heatmapInstance.dispose();
          heatmapInstance = null;
        }
        if (fullscreenChartInstance) {
          fullscreenChartInstance.dispose();
          fullscreenChartInstance = null;
        }
      } catch (error) {
        console.error('dispose方法执行出错:', error);
      }
    },
    updateData: (floor) => {
      try {
        updateChartData(floor || currentFloor.value);
      } catch (error) {
        console.error('更新数据出错:', error);
      }
    },
  });
</script>
