import { loadModelFromCache } from '@/views/scene/lib/load/modelCacheUtils';
import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
import { SceneManager } from '../SceneManager';
import { useGlobSetting } from '/@/hooks/setting';
import { buildingData } from '../../../../data/buildingData';
import { ModelCache } from './ModelCache';
import { initializeLoader, validateModelUrl, createDefaultPlaceholderBox, updatePlaceholderAnimation, removePlaceholder } from './LoaderUtils';
import { BuildingManager } from './BuildingManager';
import * as THREE from 'three';
import { LightingManager } from '../LightingManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { GridFloorManager } from '../effects/GridFloorManager';
import { MeshMerger } from '../../utils/MeshMerger';
import { throttle } from 'lodash-es';
import { isFloorDevice } from '../../utils/deviceIdentifier';

const loadedModels = new Map<string, THREE.Group | THREE.Scene>();

export class ModelLoaderManager {
  private static instance: ModelLoaderManager | null = null;
  private scene: THREE.Scene | null = null;
  private modelUrl = '';
  private globalThreeStore = {} as ReturnType<typeof useGlobalThreeStore>;
  private buildingData = {} as typeof buildingData;
  private loader = {} as any;
  private dracoLoader = {} as any;
  private modelCache = new ModelCache();
  private currentModels: Array<THREE.Group | THREE.Scene> = [];
  private interactableObjects: THREE.Mesh[] = [];
  private onModelLoadedCallbacks: Array<() => void> = [];
  private buildingManager = {} as BuildingManager;
  private modelAnimations = new Map<string, THREE.AnimationClip[]>();
  private loadedModels: Map<string, THREE.Group | THREE.Scene> = loadedModels;
  private glowMaterial = {} as THREE.MeshBasicMaterial;
  private rgbGlowMaterial = {} as THREE.ShaderMaterial;
  private cloudIconMaterial = {} as THREE.ShaderMaterial;
  private colorAnimState = {
    hue: 0,
    direction: 1,
    speed: 0.005, // 增加动画速度，让颜色变化更明显
    flow: 0,
    flowDirection: 1,
    flowSpeed: 0.01,
    lastUpdate: 0,
  };
  private _rgbState = { hue: 0, direction: 1 };
  private _cloudIconState = { intensity: 0.0, direction: 1, speed: 0.01 };
  private animationFrames = new Set<number>();
  private parkModel: THREE.Scene | null = null;
  private animationLoop: boolean = false;
  private _weatherChangeListener: ((event: CustomEvent<{ weatherType: string }>) => void) | null = null;
  private silentLoading: {
    active: boolean;
    totalModels: number;
    loadedModels: number;
    progress: number;
    modelProgress: Map<string, number>;
  } = {
    active: false,
    totalModels: 0,
    loadedModels: 0,
    progress: 0,
    modelProgress: new Map<string, number>(),
  };
  private placeholderBoxes = new Map<string, THREE.Group>();
  private placeholderAnimationEnabled = true;
  private placeholderAnimationId: number | null = null;
  private lastTime = 0;
  private geometryCache: Map<string, { geometry: THREE.BufferGeometry; useCount: number }> | null = null;
  private rgbMaterials: Set<THREE.Material> | null = null;
  private _interactableObjectsBackup: THREE.Mesh[] | null = null;
  private _cachedObjects: THREE.Object3D[] = [];
  private _updateCachedObjectsThrottled = {} as (() => void) & { cancel: () => void };
  // PPT模式专用：模型实例缓存，避免重复克隆
  private pptModelInstances: Map<string, THREE.Object3D> = new Map();

  constructor() {
    if (ModelLoaderManager.instance) return ModelLoaderManager.instance;

    const globalThreeStore = useGlobalThreeStore();
    const { modelUrl } = useGlobSetting();

    this.scene = SceneManager.getInstance().scene;
    this.modelUrl = modelUrl || '';
    this.globalThreeStore = globalThreeStore;
    this.buildingData = buildingData;

    const { loader, dracoLoader } = initializeLoader(modelUrl || '');
    this.loader = loader;
    this.dracoLoader = dracoLoader;

    this.modelCache = new ModelCache();

    this.currentModels = [];
    this.interactableObjects = [];
    this.onModelLoadedCallbacks = [];

    this.buildingManager = new BuildingManager(this);

    this.modelAnimations = new Map<string, any>();
    this.loadedModels = loadedModels;

    // 修改发光材质的创建
    this.glowMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 1,
      side: THREE.DoubleSide,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
    });

    this.rgbGlowMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(0x00ffff) },
        speed: { value: 0.5 },
        intensity: { value: 2.0 },
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;
        void main() {
          vUv = uv;
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color;
        uniform float speed;
        uniform float intensity;
        varying vec2 vUv;
        varying vec3 vPosition;

        void main() {
          float flow = fract(vUv.x - time * speed);
          float glow = smoothstep(0.0, 0.5, flow) * smoothstep(1.0, 0.5, flow);
          vec3 finalColor = color * (1.0 + intensity * glow);
          gl_FragColor = vec4(finalColor, 1.0);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
    });

    this.cloudIconMaterial = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: new THREE.Color(0x00ffff) },
        intensity: { value: 0.5 },
        flow: { value: 0.0 },
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float intensity;
        uniform float flow;
        varying vec2 vUv;

        void main() {
          vec3 finalColor = color * (0.7 + 0.3 * flow);
          float alpha = 0.9;
          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
    });

    this.colorAnimState = {
      hue: 0,
      direction: 1,
      speed: 0.0001,
      flow: 0,
      flowDirection: 1,
      flowSpeed: 0.01,
      lastUpdate: 0,
    };
    this._rgbState = { hue: 0, direction: 1 };
    this._cloudIconState = { intensity: 0.0, direction: 1, speed: 0.01 };
    this.animationFrames = new Set<number>();
    this.parkModel = null;
    this.animationLoop = false;
    this._weatherChangeListener = null;

    this.silentLoading = {
      active: false,
      totalModels: 0,
      loadedModels: 0,
      progress: 0,
      modelProgress: new Map<string, number>(),
    };

    this.placeholderBoxes = new Map<string, THREE.Group>();
    this.placeholderAnimationEnabled = true;
    this.lastTime = performance.now();
    this.geometryCache = null;
    this.rgbMaterials = null;
    this._interactableObjectsBackup = null;

    this._setupWeatherChangeListener();
    this._setupPlaceholderAnimationLoop();

    // 创建节流版本的更新函数
    this._updateCachedObjectsThrottled = throttle(this._updateCachedObjects.bind(this), 100);

    ModelLoaderManager.instance = this;

    // 添加全局调试函数
    (window as any).debugScene = () => this.debugSceneObjects();
    (window as any).refreshLights = () => this._refreshBuildingLights();
  }

  static getInstance(): ModelLoaderManager {
    if (!ModelLoaderManager.instance) {
      ModelLoaderManager.instance = new ModelLoaderManager();
    }
    return ModelLoaderManager.instance;
  }

  addModelLoadedCallback(callback: () => void): void {
    this.onModelLoadedCallbacks.push(callback);
  }

  private _triggerModelLoadedCallbacks(): void {
    this.onModelLoadedCallbacks.forEach((callback) => callback());
  }

  async loadModelWithCache(path: string, type: string, _version = '1.0'): Promise<void> {
    if (this.loadedModels.has(path)) {
      const existingModel = this.loadedModels.get(path);
      if (existingModel) {
        this.scene!.add(existingModel);
        this._processModel(existingModel as THREE.Scene, type, path);
        this._triggerModelLoadedCallbacks();
        return;
      }
    }

    this._createModelPlaceholder(path, type);

    try {
      for (let progress = 1; progress <= 99; progress += 10) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        this.globalThreeStore.setLoadModelLoading(Math.floor(progress));
      }

      const cachedScene = await loadModelFromCache(path, this.loader);
      if (cachedScene) {
        this._removePlaceholder(path);
        this._processModel(cachedScene, type, path);
        this.globalThreeStore.setLoadModelLoading(100);
        this.loadedModels.set(path, cachedScene as unknown as THREE.Group);
        // 移除这里的回调触发，让_processModel中的_finalizeModelProcessing来处理
        return;
      }
    } catch (error: any) {
      console.error('解析缓存模型出错:', error);
      this.globalThreeStore.addLoadingError({ message: `解析缓存模型出错: ${error.message}`, timestamp: Date.now() });
    }

    await this.loadModelFromNetwork(path, type);
  }

  loadModelFromNetwork(path: string, type: string): Promise<THREE.Scene> {
    return new Promise(async (resolve, reject) => {
      try {
        const validation = await validateModelUrl(path);
        if (!validation.valid) {
          console.error(`模型URL验证失败:`, validation.message);
          this.globalThreeStore.setLoadModelLoading(0);
          this.globalThreeStore.addLoadingError({ message: `模型URL验证失败: ${validation.message}`, timestamp: Date.now() });
          this._showUserFriendlyError(`模型加载失败: ${validation.message}`);
          reject(new Error(`模型URL验证失败: ${validation.message}`));
          return;
        }

        if (!this.placeholderBoxes.has(path)) {
          this._createModelPlaceholder(path, type);
        }

        // URL验证通过，开始加载模型

        let attempts = 0;
        const maxAttempts = 3;

        const loadWithRetry = () => {
          attempts++;
          // 只在开发环境输出加载尝试日志
          if (process.env.NODE_ENV === 'development') {
            console.log(`尝试加载模型 (${attempts}/${maxAttempts}): ${path}`);
          }

          this.loader.load(
            path,
            (gltf: unknown) => {
              // 模型加载成功，只在开发环境输出日志
              if (process.env.NODE_ENV === 'development') {
                console.log(`模型加载成功: ${path}`);
              }
              this._removePlaceholder(path);
              const model = (gltf as { scene: THREE.Scene }).scene;
              if (
                (gltf as { animations: THREE.AnimationClip[] }).animations &&
                (gltf as { animations: THREE.AnimationClip[] }).animations.length > 0
              ) {
                this.modelAnimations.set(type, (gltf as { animations: THREE.AnimationClip[] }).animations);
              }
              this._processModel(model, type, path);
              this.globalThreeStore.setLoadModelLoading(100);
              this.loadedModels.set(path, model as unknown as THREE.Group);
              // 移除这里的回调触发，让_processModel中的_finalizeModelProcessing来处理
              resolve(model);
            },
            (xhr: ProgressEvent) => {
              const progress = Math.min(99, Math.max(1, Math.floor((xhr.loaded / xhr.total) * 100)));
              this.globalThreeStore.setLoadModelLoading(progress);
            },
            (error: unknown) => {
              console.error(`加载GLTF数据出错 (${attempts}/${maxAttempts}):`, error);
              if (error instanceof Error && error.toString().includes('is not valid JSON')) {
                console.error('收到HTML而非GLTF文件，可能是服务器配置问题或URL错误');
                fetch(path)
                  .then((response) => response.text())
                  .then((text) => {
                    console.error('服务器返回内容的前100个字符:', text.substring(0, 100));
                  })
                  .catch((fetchError) => {
                    console.error('获取响应内容失败:', fetchError);
                  });
              }

              if (attempts < maxAttempts) {
                if (process.env.NODE_ENV === 'development') {
                  console.log(`${attempts}/${maxAttempts} 次尝试失败，将在1秒后重试...`);
                }
                setTimeout(loadWithRetry, 1000);
              } else {
                console.error(`已达到最大尝试次数 (${maxAttempts})，放弃加载`);
                this.globalThreeStore.setLoadModelLoading(0);
                this._removePlaceholder(path);
                this._showUserFriendlyError(`模型加载失败，请检查网络连接或联系管理员`);
                reject(error);
              }
            }
          );
        };

        loadWithRetry();
      } catch (error: any) {
        console.error(`加载模型过程中发生异常:`, error);
        this.globalThreeStore.addLoadingError({ message: `加载模型过程中发生异常: ${error.message}`, timestamp: Date.now() });
        this._removePlaceholder(path);
        this.globalThreeStore.setLoadModelLoading(0);
        reject(error);
      }
    });
  }

  private _showUserFriendlyError(message: string): void {
    console.error(`[用户提示] ${message}`);
    if (window.$message) {
      window.$message.error(message);
    }
  }

  getAnimations(type: string): THREE.AnimationClip[] {
    return this.modelAnimations.get(type) || [];
  }

  async removeCurrentModel(): Promise<void> {
    this.animationFrames.forEach((frameId) => {
      cancelAnimationFrame(frameId);
    });
    this.animationFrames.clear();

    this._rgbState = { hue: 0, direction: 1 };
    this.placeholderBoxes.forEach((_box, path) => this._removePlaceholder(path));
    this.placeholderBoxes.clear();

    // 清理MeshMerger中的合并数据，防止内存泄漏和性能问题
    if (this.scene) {
      const { meshMerger } = await import('@/views/scene/utils/MeshMerger');
      meshMerger.dispose(this.scene);
      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] 已清理MeshMerger合并数据');
      }
    }

    // 清理可交互对象数组，防止raycasting性能问题
    this.interactableObjects = [];
    if (process.env.NODE_ENV === 'development') {
      console.log('[ModelLoaderManager] 已清理interactableObjects数组');
    }

    if (this.currentModels.length === 0) {
      console.warn('没有模型需要移除');
      return;
    }

    while (this.currentModels.length > 0) {
      const model = this.currentModels.pop();
      if (model && (model.userData.type !== 'park' || model !== this.parkModel)) {
        this.scene!.remove(model as THREE.Object3D);
        model.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh) {
            if (child.geometry) {
              child.geometry.dispose();
            }
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: THREE.Material) => mat.dispose());
            } else if (child.material) {
              (child.material as THREE.Material).dispose();
            }
          }
        });
      }
    }
  }

  preloadModels(modelPaths: string[]): void {
    modelPaths.forEach((path) => {
      if (!this.loadedModels.has(path)) {
        this.loader.load(path, (gltf: unknown) => {
          const model = (gltf as { scene: THREE.Scene }).scene;
          this.loadedModels.set(path, model as unknown as THREE.Group);
        });
      }
    });
  }
  async showBuilding(buildingPath: string): Promise<THREE.Scene> {
    try {
      const result = await this.buildingManager.switchToBuilding(buildingPath);
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        if (sceneManager.renderer) {
          sceneManager.renderer.setClearColor(0x87ceeb, 1);
        }
        if (sceneManager.skyManager) {
          sceneManager.skyManager.show();
        }
        sceneManager.setDayLighting();

        // 应用外部场景光照设置
        const lightingManager = LightingManager.getInstance();
        if (lightingManager) {
          lightingManager.adjustLightingForWeather('clear');
        }

        // 应用平衡的色调映射，避免建筑过度曝光
        const renderingPipeline = RenderingPipeline.getInstance();
        if (renderingPipeline) {
          renderingPipeline.setToneMapping(THREE.ACESFilmicToneMapping, 0.9, true);
        }
      }

      // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
      this._refreshBuildingLights();
      return result as unknown as THREE.Scene;
    } catch (error) {
      console.error('切换到建筑模式失败:', error);
      throw error;
    }
  }

  async fadeOutCurrentFloor(): Promise<void> {
    const currentFloor = this.getCurrentFloorModel();
    if (!currentFloor) return;

    return new Promise((resolve) => {
      // 保存原始材质属性并设置透明度
      currentFloor.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh && child.material) {
          // 保存原始材质类型信息
          if (!child.userData.originalMaterialType) {
            if (Array.isArray(child.material)) {
              child.userData.originalMaterialType = child.material.map(
                (mat) =>
                  mat.type ||
                  (mat instanceof THREE.MeshStandardMaterial
                    ? 'MeshStandardMaterial'
                    : mat instanceof THREE.MeshPhysicalMaterial
                      ? 'MeshPhysicalMaterial'
                      : mat instanceof THREE.MeshPhongMaterial
                        ? 'MeshPhongMaterial'
                        : 'MeshBasicMaterial')
              );
            } else {
              child.userData.originalMaterialType =
                child.material.type ||
                (child.material instanceof THREE.MeshStandardMaterial
                  ? 'MeshStandardMaterial'
                  : child.material instanceof THREE.MeshPhysicalMaterial
                    ? 'MeshPhysicalMaterial'
                    : child.material instanceof THREE.MeshPhongMaterial
                      ? 'MeshPhongMaterial'
                      : 'MeshBasicMaterial');
            }
          }

          // 设置透明度
          if (Array.isArray(child.material)) {
            child.material.forEach((material: THREE.Material) => {
              material.transparent = true;
              material.opacity = 1;
              material.needsUpdate = true;
            });
          } else {
            child.material.transparent = true;
            child.material.opacity = 1;
            child.material.needsUpdate = true;
          }
        }
      });

      const startTime = Date.now();
      const duration = 500;

      const animate = () => {
        const progress = (Date.now() - startTime) / duration;
        if (progress >= 1) {
          currentFloor.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh && child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material: THREE.Material) => {
                  material.opacity = 0;
                  material.needsUpdate = true;
                });
              } else {
                child.material.opacity = 0;
                child.material.needsUpdate = true;
              }
            }
          });
          currentFloor.visible = false;
          resolve();
          return;
        }

        currentFloor.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                material.opacity = 1 - progress;
                material.needsUpdate = true;
              });
            } else {
              child.material.opacity = 1 - progress;
              child.material.needsUpdate = true;
            }
          }
        });

        requestAnimationFrame(animate);
      };

      animate();
    });
  }

  async fadeInFloorModel(floorModel: THREE.Object3D): Promise<void> {
    if (!floorModel) return;

    return new Promise((resolve) => {
      // 确保材质设置正确并保留原始属性
      floorModel.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh && child.material) {
          // 确保使用正确的材质类型
          if (child.userData.originalMaterialType) {
            // 保留原始材质类型
          } else {
            // 如果没有保存原始材质类型，尝试使用MeshStandardMaterial
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                // 保留材质的所有属性，只设置透明度
                material.transparent = true;
                material.opacity = 0;
                material.needsUpdate = true;
              });
            } else {
              // 保留材质的所有属性，只设置透明度
              child.material.transparent = true;
              child.material.opacity = 0;
              child.material.needsUpdate = true;
            }
          }
        }
      });

      floorModel.visible = true;
      const startTime = Date.now();
      const duration = 500;

      const animate = () => {
        const progress = (Date.now() - startTime) / duration;
        if (progress >= 1) {
          floorModel.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh && child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material: THREE.Material) => {
                  material.opacity = 1;
                  material.needsUpdate = true;
                });
              } else {
                child.material.opacity = 1;
                child.material.needsUpdate = true;
              }
            }
          });
          resolve();
          return;
        }

        floorModel.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                material.opacity = progress;
                material.needsUpdate = true;
              });
            } else {
              child.material.opacity = progress;
              child.material.needsUpdate = true;
            }
          }
        });

        requestAnimationFrame(animate);
      };

      animate();
    });
  }

  async showFloor(floorId: string): Promise<THREE.Object3D> {
    try {
      await this.fadeOutCurrentFloor();
      const floorModel = await this.buildingManager.switchToFloor(floorId);
      if (!floorModel) {
        throw new Error('新楼层模型加载失败');
      }

      // 获取楼层编号 (1-6)
      const floorNumber = parseInt(floorId.match(/\d+/)?.[0] || '1');

      // 根据楼层设置不同的合并参数
      const mergeOptions = {
        enabled: true,
        maxTriangles: floorNumber === 1 ? 20000 : 30000, // 1楼使用更小的合并面数
        mergeDifferentMaterials: false,
        verbose: true,
      };

      // 优化：强制执行网格合并
      const meshMerger = MeshMerger.getInstance();
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] 开始执行${floorNumber}楼模型网格合并优化...`);
      }

      const mergeResult = meshMerger.mergeMeshes(floorModel, mergeOptions);

      if (mergeResult.mergedMeshCount > 0 && process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] ${floorNumber}楼网格合并完成: ${mergeResult.originalMeshCount} -> ${mergeResult.mergedMeshCount} 网格`);
      }

      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        if (sceneManager.renderer) {
          sceneManager.renderer.setClearColor(0x111111, 1);
        }
        if (sceneManager.skyManager) {
          sceneManager.skyManager.hideSky();
        }
        sceneManager.setNightLighting();
        const lightingManager = LightingManager.getInstance();
        if (lightingManager) {
          lightingManager.adjustLightingForWeather('dark');
        }
        sceneManager.switchSceneType('interior');

        // 网格合并后重新初始化交互对象
        this._rebuildInteractableObjects();

        setTimeout(() => {
          const gridFloorManager = GridFloorManager.getInstance();
          const gridFloor = gridFloorManager.gridFloor;
          if (!gridFloor || !sceneManager.scene.children.includes(gridFloor)) {
            console.warn('[ModelLoaderManager] 网格地板丢失，尝试重新显示');
            gridFloorManager.show();
          }
        }, 200);
      }

      await this.fadeInFloorModel(floorModel);

      // 触发场景对象变化事件，确保所有监听器更新
      window.dispatchEvent(new CustomEvent('scene-objects-changed'));

      return floorModel;
    } catch (error) {
      console.error('楼层切换失败:', error);
      throw error;
    }
  }

  async showExterior(): Promise<void> {
    const sceneManager = SceneManager.getInstance();
    sceneManager.switchSceneType('exterior');

    // 确保应用外部场景光照设置
    const lightingManager = LightingManager.getInstance();
    if (lightingManager) {
      lightingManager.adjustLightingForWeather('clear');
    }

    // 确保应用平衡的色调映射，避免建筑过度曝光
    const renderingPipeline = RenderingPipeline.getInstance();
    if (renderingPipeline) {
      renderingPipeline.setToneMapping(THREE.ACESFilmicToneMapping, 0.9, true);
    }

    // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
    this._refreshBuildingLights();
  }

  /**
   * 仅加载外景模型而不切换场景类型
   * 用于PPT视角绑定中的外景预览，避免组件关闭
   */
  async loadExteriorModelOnly(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] 仅加载外景模型，不切换场景类型');
      }

      // 获取建筑数据
      const { buildingData } = await import('@/data/buildingData');
      if (!buildingData.modelPath) {
        throw new Error('缺少外景模型路径');
      }

      // 隐藏当前楼层模型（如果有的话）
      await this.fadeOutCurrentFloor();

      // 加载外景模型但不切换场景类型
      await this.loadModelWithCache(buildingData.modelPath, 'exterior');

      // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
      this._refreshBuildingLights();

      // 移除这里的回调触发，让_processModel中的_finalizeModelProcessing来处理

      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] 外景模型加载完成，场景类型保持不变');
      }
    } catch (error) {
      console.error('[ModelLoaderManager] 加载外景模型失败:', error);
      throw error;
    }
  }

  /**
   * PPT演示模式专用：超轻量级楼层切换（跳过所有非必要操作）
   */
  async showFloorForPPT(floorId: string): Promise<THREE.Object3D> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] PPT超轻量级切换到楼层: ${floorId}`);
      }

      // PPT模式：跳过淡入淡出动画，直接切换
      await this.fastSwitchCurrentFloor();

      // 调试：切换前状态
      this.debugPPTSceneState();

      // 优先使用PPT专用实例缓存
      const cacheKey = `floor_${floorId}`;
      if (this.pptModelInstances.has(cacheKey)) {
        const cachedInstance = this.pptModelInstances.get(cacheKey)!;
        if (this.scene) {
          // 创建新的实例而不是直接使用缓存的实例，避免重复添加到场景
          const instanceClone = cachedInstance.clone();
          instanceClone.userData.type = 'floor';
          instanceClone.visible = true;

          this.scene.add(instanceClone);
          this.currentModels.push(instanceClone as THREE.Group | THREE.Scene);

          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] PPT使用缓存实例克隆: ${floorId}`);
          }
          return instanceClone;
        }
      }

      const floorModel = await this.buildingManager.switchToFloor(floorId);
      if (!floorModel) {
        throw new Error('新楼层模型加载失败');
      }

      // 缓存实例供下次使用
      this.pptModelInstances.set(cacheKey, floorModel);

      // 🚀 PPT模式：跳过所有场景环境设置，直接显示模型
      await this.fastShowFloorModel(floorModel);

      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] PPT超轻量级楼层切换完成: ${floorId}`);
      }

      return floorModel;
    } catch (error) {
      console.error('PPT超轻量级楼层切换失败:', error);
      throw error;
    }
  }

  /**
   * PPT演示模式专用：超轻量级外景切换（跳过所有非必要操作）
   */
  async loadExteriorForPPT(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] PPT超轻量级切换到外景');
      }

      // PPT模式：跳过淡出动画，直接隐藏当前楼层模型
      await this.fastSwitchCurrentFloor();

      // 调试：切换前状态
      this.debugPPTSceneState();

      // 优先使用PPT专用实例缓存
      const cacheKey = 'exterior_main';
      if (this.pptModelInstances.has(cacheKey)) {
        const cachedInstance = this.pptModelInstances.get(cacheKey)!;
        if (this.scene) {
          // 创建新的实例而不是直接使用缓存的实例，避免重复添加到场景
          const instanceClone = cachedInstance.clone();
          instanceClone.userData.type = 'exterior';
          instanceClone.visible = true;

          this.scene.add(instanceClone);
          this.currentModels.push(instanceClone as THREE.Group | THREE.Scene);
          this.parkModel = instanceClone as THREE.Scene;

          if (process.env.NODE_ENV === 'development') {
            console.log('[ModelLoaderManager] PPT使用缓存外景实例克隆');
          }
          return;
        }
      }

      // 获取建筑数据
      const { buildingData } = await import('@/data/buildingData');
      if (!buildingData.modelPath) {
        throw new Error('缺少外景模型路径');
      }

      // PPT模式：使用超轻量级外景加载
      await this._loadExteriorModelUltraLight(buildingData.modelPath);

      // 缓存当前外景模型实例
      if (this.parkModel) {
        this.pptModelInstances.set(cacheKey, this.parkModel);
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] PPT超轻量级外景切换完成');
      }
    } catch (error) {
      console.error('[ModelLoaderManager] PPT超轻量级外景切换失败:', error);
      throw error;
    }
  }

  /**
   * 超轻量级外景模型加载（PPT专用 - 跳过所有额外处理）
   */
  private async _loadExteriorModelUltraLight(modelPath: string): Promise<void> {
    try {
      // 检查是否已经加载过
      if (this.loadedModels.has(modelPath)) {
        const cachedModel = this.loadedModels.get(modelPath);
        if (cachedModel && this.scene) {
          // 直接使用缓存的模型，跳过所有处理
          const model = cachedModel.clone();
          model.userData.type = 'exterior';
          model.position.set(0, -2.7, 0);
          model.visible = true;

          this.scene.add(model);
          this.currentModels.push(model);
          this.parkModel = model as THREE.Scene;

          if (process.env.NODE_ENV === 'development') {
            console.log('[ModelLoaderManager] PPT超轻量级使用缓存外景模型');
          }
          return;
        }
      }

      // 如果没有缓存，直接加载（最小化处理）
      const gltf = await new Promise<any>((resolve, reject) => {
        this.loader.load(modelPath, resolve, undefined, reject);
      });

      const model = gltf.scene as THREE.Scene;
      model.userData.type = 'exterior';
      model.position.set(0, -2.7, 0);
      model.visible = true;

      if (this.scene) {
        this.scene.add(model);
        this.currentModels.push(model);
        this.parkModel = model;

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] PPT超轻量级外景加载完成，当前模型数量: ${this.currentModels.length}`);
        }
      }

      // 缓存模型
      this.loadedModels.set(modelPath, model);

      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] PPT超轻量级直接加载外景模型完成');
      }
    } catch (error) {
      console.error('[ModelLoaderManager] PPT超轻量级直接加载外景模型失败:', error);
      throw error;
    }
  }

  /**
   * PPT模式：跳过所有材质修改，保持原始外观（保留所有细节）
   */
  private _applyLightingEffectsOnly(model: THREE.Object3D): void {
    let dengtiaoCount = 0;
    let allMeshNames: string[] = [];

    model.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        allMeshNames.push(child.name);

        // 检测dengtiao模型但不修改材质，保留原始外观
        if (this._isDengtiaoMesh(child)) {
          dengtiaoCount++;

          // 只标记为dengtiao设备，不修改材质
          child.userData.isDengtiaoDevice = true;
          child.userData.preserveOriginalMaterial = true; // 标记保留原始材质
          child.visible = true;

          if (process.env.NODE_ENV === 'development') {
            console.log(`[PPT轻量级] 发现DengTiao模型但保留原始材质: ${child.name}`);
          }
        }

        // 云图标也跳过处理，保持原始外观
        if (child.name.includes('云')) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[PPT轻量级] 发现云模型但保留原始材质: ${child.name}`);
          }
        }
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[PPT轻量级] 完成检测 - 总网格数: ${allMeshNames.length}, DengTiao数量: ${dengtiaoCount} (保留原始材质)`);

      // 查找所有可能的DengTiao相关名称
      const dengtiaoNames = allMeshNames.filter(
        (name) =>
          name.includes('DengTiao') ||
          name.includes('Dengtiao') ||
          name.includes('dengtiao') ||
          name.includes('DENGTIAO') ||
          name.includes('Wailing_DengTiao')
      );
      console.log(`[PPT轻量级] 发现的DengTiao相关名称:`, dengtiaoNames);

      // 如果没有找到DengTiao，显示所有网格名称用于调试
      if (dengtiaoCount === 0) {
        console.log(`[PPT轻量级] 警告：未找到DengTiao模型，所有网格名称:`, allMeshNames);
      }
    }
  }

  /**
   * 创建保留原始材质特性的增强发光材质
   */
  private _createEnhancedGlowMaterial(originalMaterial: THREE.Material): THREE.ShaderMaterial {
    const original = originalMaterial as any;

    // 创建增强着色器材质，保留原始材质的纹理和特性
    const enhancedMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        glowColor: { value: new THREE.Color(0x00ffff) },
        speed: { value: 0.5 },
        intensity: { value: 2.0 },
        // 保留原始材质的纹理
        map: { value: original.map || null },
        normalMap: { value: original.normalMap || null },
        roughnessMap: { value: original.roughnessMap || null },
        metalnessMap: { value: original.metalnessMap || null },
        // 保留原始材质属性
        baseColor: { value: original.color || new THREE.Color(0xffffff) },
        roughness: { value: original.roughness || 0.5 },
        metalness: { value: original.metalness || 0.0 },
        opacity: { value: original.opacity || 1.0 },
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewPosition;
        
        void main() {
          vUv = uv;
          vPosition = position;
          vNormal = normalize(normalMatrix * normal);
          
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          vViewPosition = -mvPosition.xyz;
          
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 glowColor;
        uniform float speed;
        uniform float intensity;
        uniform sampler2D map;
        uniform sampler2D normalMap;
        uniform vec3 baseColor;
        uniform float roughness;
        uniform float metalness;
        uniform float opacity;
        
        varying vec2 vUv;
        varying vec3 vPosition;
        varying vec3 vNormal;
        varying vec3 vViewPosition;

        void main() {
          // 基础颜色（保留原始纹理）
          vec3 finalColor = baseColor;
          if (map != null) {
            vec4 texColor = texture2D(map, vUv);
            finalColor = texColor.rgb * baseColor;
          }
          
          // 计算发光效果
          float flow = fract(vUv.x - time * speed);
          float glow = smoothstep(0.0, 0.5, flow) * smoothstep(1.0, 0.5, flow);
          
          // 边缘发光增强
          vec3 viewDir = normalize(vViewPosition);
          float edgeFactor = 1.0 - abs(dot(vNormal, viewDir));
          edgeFactor = pow(edgeFactor, 2.0);
          
          // 混合发光效果和原始颜色
          vec3 glowEffect = glowColor * (intensity * glow + edgeFactor * 0.8);
          finalColor = finalColor + glowEffect;
          
          gl_FragColor = vec4(finalColor, opacity);
        }
      `,
      transparent: original.transparent || false,
      side: THREE.DoubleSide,
      depthWrite: true,
    });

    // 复制原始材质的其他重要属性
    if (original.alphaTest !== undefined) enhancedMaterial.alphaTest = original.alphaTest;
    if (original.depthTest !== undefined) enhancedMaterial.depthTest = original.depthTest;

    return enhancedMaterial;
  }

  /**
   * 恢复dengtiao模型的原始材质（调试用）
   */
  public restoreDengtiaoOriginalMaterials(): void {
    if (!this.scene) return;

    let restoredCount = 0;
    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh && child.userData.isDengtiaoDevice && child.userData.originalMaterial) {
        child.material = child.userData.originalMaterial;
        child.userData.isRGBLight = false;
        child.userData.isDengtiaoDevice = false;
        restoredCount++;

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] 恢复原始材质: ${child.name}`);
        }
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 已恢复 ${restoredCount} 个dengtiao模型的原始材质`);
    }
  }

  /**
   * 切换dengtiao发光效果（调试用）
   */
  public toggleDengtiaoGlowEffect(enable: boolean): void {
    if (!this.scene) return;

    let processedCount = 0;
    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh && this._isDengtiaoMesh(child)) {
        if (enable && child.userData.originalMaterial) {
          // 应用发光效果
          const enhancedMaterial = this._createEnhancedGlowMaterial(child.userData.originalMaterial);
          child.material = enhancedMaterial;
          child.userData.isRGBLight = true;
          child.userData.isDengtiaoDevice = true;
          this._trackRGBMaterial(enhancedMaterial);
        } else if (!enable && child.userData.originalMaterial) {
          // 恢复原始材质
          child.material = child.userData.originalMaterial;
          child.userData.isRGBLight = false;
          child.userData.isDengtiaoDevice = false;
        } else if (enable && !child.userData.originalMaterial) {
          // 保存当前材质作为原始材质
          child.userData.originalMaterial = child.material;
          const enhancedMaterial = this._createEnhancedGlowMaterial(child.material as THREE.Material);
          child.material = enhancedMaterial;
          child.userData.isRGBLight = true;
          child.userData.isDengtiaoDevice = true;
          this._trackRGBMaterial(enhancedMaterial);
        }
        processedCount++;
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] ${enable ? '启用' : '禁用'}了 ${processedCount} 个dengtiao模型的发光效果`);
    }
  }

  /**
   * 对比调试：展示PPT模式vs正常模式的dengtiao处理差异
   */
  public debugDengtiaoProcessingDifference(): void {
    if (!this.scene) return;

    console.log('======= Dengtiao模型处理差异对比 =======');

    let pptModeCount = 0;
    let normalModeCount = 0;
    let preservedMaterialCount = 0;
    let modifiedMaterialCount = 0;

    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh && this._isDengtiaoMesh(child)) {
        const hasPreservedFlag = child.userData.preserveOriginalMaterial;
        const hasGlowEffect = child.userData.isRGBLight;
        const hasOriginalMaterial = child.userData.originalMaterial;

        if (hasPreservedFlag) {
          pptModeCount++;
          preservedMaterialCount++;
          console.log(`[PPT模式] ${child.name}: 保留原始材质`);
        } else if (hasGlowEffect) {
          normalModeCount++;
          modifiedMaterialCount++;
          console.log(`[正常模式] ${child.name}: 应用发光材质`);
        }

        // 详细材质信息
        const material = child.material as any;
        if (material.type === 'ShaderMaterial') {
          console.log(`  材质类型: ${material.type} (自定义着色器)`);
          console.log(`  Uniforms: ${Object.keys(material.uniforms || {}).join(', ')}`);
        } else {
          console.log(`  材质类型: ${material.type}`);
          console.log(`  颜色: ${material.color ? material.color.getHexString() : 'N/A'}`);
          console.log(`  纹理: ${material.map ? 'Yes' : 'No'}`);
        }
      }
    });

    console.log('======= 统计结果 =======');
    console.log(`PPT模式处理的dengtiao: ${pptModeCount} (保留原始材质)`);
    console.log(`正常模式处理的dengtiao: ${normalModeCount} (应用发光效果)`);
    console.log(`保留原始材质的模型: ${preservedMaterialCount}`);
    console.log(`修改材质的模型: ${modifiedMaterialCount}`);

    if (pptModeCount > 0 && normalModeCount === 0) {
      console.log('✅ 当前在PPT模式，dengtiao细节应该完整保留');
    } else if (normalModeCount > 0 && pptModeCount === 0) {
      console.log('⚠️ 当前在正常模式，dengtiao可能已应用发光效果');
    } else {
      console.log('⚠️ 混合状态，部分dengtiao可能被不同方式处理');
    }
    console.log('=====================================');
  }

  private _processModel(model: THREE.Scene, type: string, modelPath?: string): void {
    model.userData.type = type;
    model.position.set(0, 0, 0);

    if (type === 'exterior') {
      this.parkModel = model;
      model.position.y -= 2.7;

      // 遍历和处理模型
      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] 开始遍历和处理模型...');
      }
      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          // 只在开发环境打印模型名称
          if (process.env.NODE_ENV === 'development') {
            console.log('[ModelLoaderManager] 找到模型:', child.name);
          }

          // 检查模型名称（增加更多变体的检查）
          if (
            child.name.includes('DengTiao') ||
            child.name.includes('Dengtiao') ||
            child.name.includes('dengtiao') ||
            child.name.includes('DENGTIAO') ||
            child.name.includes('Wailing_DengTiao') ||
            child.name === 'Wailing_DengTiao.001'
          ) {
            if (process.env.NODE_ENV === 'development') {
              console.log('[ModelLoaderManager] 找到灯带模型:', child.name);
            }

            // 创建发光材质
            const rgbMaterial = this.rgbGlowMaterial.clone();
            rgbMaterial.uniforms.intensity.value = 3.0; // 增加发光强度
            rgbMaterial.uniforms.speed.value = 0.5; // 调整动画速度
            rgbMaterial.transparent = true;
            rgbMaterial.side = THREE.DoubleSide;

            // 应用材质
            child.material = rgbMaterial;
            child.userData.isRGBLight = true;
            this._trackRGBMaterial(rgbMaterial);
            child.visible = true;

            // 确认材质应用
            if (process.env.NODE_ENV === 'development') {
              console.log('[ModelLoaderManager] 已应用发光材质到:', child.name);
            }
          }
        }
      });
    }

    if (!this.geometryCache) {
      this.geometryCache = new Map<string, { geometry: THREE.BufferGeometry; useCount: number }>();
    }

    if (type === 'building' || type === 'exterior') {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] 开始处理${type}类型模型的灯光效果`);
      }
      let lightObjectsFound = 0;
      const allMeshNames: string[] = [];

      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          // 记录所有网格的名称用于调试
          allMeshNames.push(child.name);

          const geoHash = child.geometry.uuid;
          if (this.geometryCache!.has(geoHash)) {
            const cachedGeometry = this.geometryCache!.get(geoHash);
            if (cachedGeometry!.useCount > 3) {
              child.geometry.dispose();
              child.geometry = cachedGeometry!.geometry;
            }
          } else {
            this.geometryCache!.set(geoHash, {
              geometry: child.geometry,
              useCount: 1,
            });
          }

          // 记录所有包含"Dengtiao"或"dengtiao"的模型名称
          if (child.name.includes('Dengtiao') || child.name.includes('dengtiao')) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[ModelLoaderManager] 发现包含"Dengtiao/dengtiao"的模型: ${child.name}`);
            }
            lightObjectsFound++;
          }

          if (child.name.includes('云')) {
            const material = this.cloudIconMaterial.clone();
            child.material = material;
            child.userData.isCloudIcon = true;
            const color = new THREE.Color();
            color.setHSL(this.colorAnimState.hue, 1.0, 0.8);
            material.uniforms.color.value.copy(color);
            if (process.env.NODE_ENV === 'development') {
              console.log(`[ModelLoaderManager] 应用云图标材质: ${child.name}`);
            }
          } else if (
            child.name.includes('Dengtiao') ||
            child.name.includes('dengtiao') ||
            child.name.includes('墙面装饰') ||
            child.name.includes('灯条') ||
            child.name.includes('灯') ||
            child.name.includes('light') ||
            child.name.includes('Light') ||
            child.name.includes('LED') ||
            child.name.includes('lamp') ||
            child.name.includes('Lamp')
          ) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[ModelLoaderManager] 应用发光材质: ${child.name}`);
            }
            if (child.name.includes('RGB')) {
              const material = this.rgbGlowMaterial.clone();
              child.material = material;
              child.userData.isRGBLight = true;
              this._trackRGBMaterial(material);
              const color = new THREE.Color();
              color.setHSL(this.colorAnimState.hue, 1.0, 0.8);
              material.uniforms.color.value.copy(color);
              if (process.env.NODE_ENV === 'development') {
                console.log(`[ModelLoaderManager] 应用RGB发光材质: ${child.name}`);
              }
            } else {
              child.material = this.glowMaterial;
              child.userData.isGlowLight = true;
              if (process.env.NODE_ENV === 'development') {
                console.log(`[ModelLoaderManager] 应用普通发光材质: ${child.name}`);
              }
            }
            child.visible = true;
          }
        }
      });

      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] ${type}模型处理完成，共发现${lightObjectsFound}个包含"灯"字的模型`);
        console.log(`[ModelLoaderManager] 模型中所有网格名称 (共${allMeshNames.length}个):`, allMeshNames);

        // 查找可能的灯光相关名称
        const lightRelatedNames = allMeshNames.filter(
          (name) =>
            name.includes('Dengtiao') ||
            name.includes('dengtiao') ||
            name.includes('灯') ||
            name.includes('light') ||
            name.includes('Light') ||
            name.includes('LIGHT') ||
            name.includes('lamp') ||
            name.includes('Lamp') ||
            name.includes('LED') ||
            name.includes('led')
        );
        console.log(`[ModelLoaderManager] 发现可能的灯光相关名称:`, lightRelatedNames);
      }

      this._setupAnimationLoop();
    }

    // 为楼层模型添加特殊效果
    if (type === 'floor') {
      // 添加F1_LD_Ziti的背光效果（和dengtiao相同）
      this._addTVBacklightEffect(model);

      // 对于楼层模型，清空并重新收集
      this.interactableObjects = [];
    }

    model.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        if (type === 'park') {
          child.castShadow = false;
          child.receiveShadow = false;
        }

        // 检查是否为设备mesh，适用于所有类型的模型
        if (this._isDeviceMesh(child)) {
          this.interactableObjects.push(child);
          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] 添加可交互设备: ${child.name} (类型: ${type})`);
          }
        }
      }
    });

    this.scene!.add(model);
    this.currentModels.push(model);
    this._cacheSceneObjects();

    // 如果是楼层或建筑模型，执行网格合并优化
    if (type === 'floor' || type === 'building' || type === 'exterior') {
      // 异步执行网格合并，完成后再触发回调
      this._performMeshMerging(model, type, modelPath)
        .then(() => {
          this._finalizeModelProcessing(type);
        })
        .catch((error) => {
          console.warn(`[ModelLoaderManager] 网格合并失败，继续完成模型处理:`, error);
          this._finalizeModelProcessing(type);
        });
    } else {
      // 对于不需要网格合并的模型，直接完成处理
      this._finalizeModelProcessing(type);
    }
  }

  /**
   * 完成模型处理的最后步骤
   */
  private _finalizeModelProcessing(type: string): void {
    // 最终更新可交互对象列表
    this._rebuildInteractableObjects();

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] ${type}模型处理完全完成，可交互对象数量: ${this.interactableObjects.length}`);
    }

    // 触发模型加载完成回调
    this._triggerModelLoadedCallbacks();
  }

  getInteractableObjects(): THREE.Mesh[] {
    // 只有在对象列表为空且场景中确实有模型时才重新收集
    if (this.interactableObjects.length === 0 && this.currentModels.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('[ModelLoaderManager] 可交互对象列表为空，尝试重新收集...');
      }
      this._rebuildInteractableObjects();
    }
    return this.interactableObjects;
  }

  /**
   * 重新构建可交互对象列表
   */
  private _rebuildInteractableObjects(): void {
    if (!this.scene) {
      console.warn('[ModelLoaderManager] 场景未初始化，无法重新构建可交互对象列表');
      return;
    }

    this.interactableObjects = [];
    let deviceCount = 0;
    let totalMeshCount = 0;

    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        totalMeshCount++;

        // 检查是否为设备mesh
        if (this._isDeviceMesh(child)) {
          this.interactableObjects.push(child);
          deviceCount++;
          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] 重新收集设备: ${child.name}`);
          }
        }
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 重新构建完成 - 总网格数: ${totalMeshCount}, 设备数: ${deviceCount}`);
    }

    // 通知ObjectSelection更新其可交互对象列表
    window.dispatchEvent(new CustomEvent('scene-objects-changed'));
  }

  dispose(): void {
    if (this.animationLoop) {
      this.animationLoop = false;
    }
    if (this.cloudIconMaterial) {
      this.cloudIconMaterial.uniforms.time.value = 0;
    }
    if (this.placeholderAnimationId) {
      cancelAnimationFrame(this.placeholderAnimationId);
      this.placeholderAnimationId = null;
    }
    this.placeholderBoxes.forEach((_box, path) => {
      this._removePlaceholder(path);
    });
    this.placeholderBoxes.clear();
    this.placeholderAnimationEnabled = false;
    this.animationFrames.forEach((frameId) => {
      cancelAnimationFrame(frameId);
    });
    this.animationFrames.clear();
    if (this.dracoLoader) {
      // @ts-ignore
      this.dracoLoader.dispose();
      // @ts-ignore
      this.dracoLoader = null;
    }
    this.loadedModels.forEach((model) => {
      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (Array.isArray(child.material)) {
            child.material.forEach((material: THREE.Material) => material.dispose());
          } else if (child.material) {
            (child.material as THREE.Material).dispose();
          }
        }
      });
    });
    this.loadedModels.clear();
    this.removeCurrentModel();
    this.scene = null;
    // @ts-ignore
    this.containerRef = null;
    // @ts-ignore
    this.buildingData = null;
    // @ts-ignore
    this.loader = null;
    this.interactableObjects = [];
    this.modelAnimations.clear();
    if (this._weatherChangeListener) {
      window.removeEventListener('weather-effect-changed', this._weatherChangeListener as EventListener);
    }
    // 取消节流函数
    if (this._updateCachedObjectsThrottled) {
      this._updateCachedObjectsThrottled.cancel();
    }
    ModelLoaderManager.instance = null;
  }

  backupInteractableObjects(): void {
    this._interactableObjectsBackup = [...this.interactableObjects];
  }

  restoreInteractableObjects(): void {
    if (this._interactableObjectsBackup) {
      this.interactableObjects = [...this._interactableObjectsBackup];
    }
  }

  async silentLoadModel(path: string, type: string = 'unknown'): Promise<void> {
    try {
      if (this.loadedModels.has(path)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('模型已在缓存中，跳过加载:', path);
        }

        // 发送缓存模型完成事件给PPT预加载器
        window.dispatchEvent(
          new CustomEvent('silent-loading-progress', {
            detail: { path, progress: 1.0 },
          })
        );

        this._incrementSilentLoadedModels();
        return;
      }
      if (process.env.NODE_ENV === 'development') {
        console.log('开始静默加载模型:', path);
      }
      const validation = await validateModelUrl(path);
      if (!validation.valid) {
        console.warn(`静默加载 - 模型URL验证失败: ${validation.message}`);
        return;
      }
      const gltf = await new Promise<unknown>((resolve, reject) => {
        this.loader.load(
          path,
          resolve,
          (xhr: ProgressEvent) => {
            if (xhr.lengthComputable) {
              const progress = Math.floor((xhr.loaded / xhr.total) * 100);
              this._updateSilentLoadingProgress(path, progress);
            }
          },
          (error: unknown) => {
            console.warn(`静默加载 - 加载失败:`, error);
            reject(error);
          }
        );
      });
      const model = (gltf as { scene: THREE.Scene }).scene;
      this.loadedModels.set(path, model as unknown as THREE.Group);
      if ((gltf as { animations: THREE.AnimationClip[] }).animations && (gltf as { animations: THREE.AnimationClip[] }).animations.length > 0) {
        this.modelAnimations.set(type, (gltf as { animations: THREE.AnimationClip[] }).animations);
      }

      // 发送模型完成事件给PPT预加载器
      window.dispatchEvent(
        new CustomEvent('silent-loading-progress', {
          detail: { path, progress: 1.0 },
        })
      );

      this._incrementSilentLoadedModels();
      if (process.env.NODE_ENV === 'development') {
        console.log('Silent model load complete:', path);
      }
    } catch (error) {
      console.warn('静默加载模型失败:', path, error);

      // 即使失败也发送完成事件给PPT预加载器
      window.dispatchEvent(
        new CustomEvent('silent-loading-progress', {
          detail: { path, progress: 1.0 },
        })
      );

      this._incrementSilentLoadedModels();
    }
  }

  async batchSilentLoad(modelPaths: string[]): Promise<void> {
    if (!modelPaths || modelPaths.length === 0) return;
    if (process.env.NODE_ENV === 'development') {
      console.log(`开始批量静默加载，模型数量: ${modelPaths.length}，路径:`, modelPaths);
    }
    this.silentLoading = {
      active: true,
      totalModels: modelPaths.length,
      loadedModels: 0,
      progress: 0,
      modelProgress: new Map<string, number>(),
    };
    this._dispatchSilentLoadingEvent();
    const globalThreeStore = this.globalThreeStore;
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[ModelLoaderManager] 静默加载前全局计数 - 总数:${globalThreeStore.totalModelsToLoad}, 已加载:${globalThreeStore.loadedModelsCount}`
      );
    }
    const expectedModels = 2 + (this.buildingData.floors ? this.buildingData.floors.length : 0);
    if (globalThreeStore.totalModelsToLoad <= expectedModels) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] 静默加载的模型已包含在计划内，不增加总数计数`);
      }
    } else {
      const currentTotal = globalThreeStore.totalModelsToLoad;
      globalThreeStore.setModelsLoadInfo(currentTotal + modelPaths.length, globalThreeStore.loadedModelsCount);
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ModelLoaderManager] 增加静默加载模型到总数计数: ${currentTotal} -> ${currentTotal + modelPaths.length}`);
      }
    }
    const loadPromises = modelPaths.map((path) => this.silentLoadModel(path));
    const results = await Promise.allSettled(loadPromises);
    const succeeded = results.filter((r) => r.status === 'fulfilled').length;
    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 批量静默加载完成，成功: ${succeeded}/${modelPaths.length}`);
    }
    for (let i = 0; i < succeeded; i++) {
      globalThreeStore.incrementLoadedModels();
    }
    this.silentLoading.progress = 100;
    this._dispatchSilentLoadingEvent();
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.onModelLoadedCallbacks.forEach((callback) => {
      try {
        callback();
      } catch (e) {
        console.error('Error in model loaded callback:', e);
      }
    });
    this.silentLoading.active = false;
    this._dispatchSilentLoadingEvent();
    if (globalThreeStore.loadedModelsCount >= globalThreeStore.totalModelsToLoad) {
      if (process.env.NODE_ENV === 'development') {
        console.log('全部模型（包括静默加载模型）已加载完成，设置应用程序就绪状态');
      }
      globalThreeStore.setLoadingComplete(true);
      globalThreeStore.setAppReady(true);
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `模型加载进度：${globalThreeStore.loadedModelsCount}/${globalThreeStore.totalModelsToLoad} (${Math.floor((globalThreeStore.loadedModelsCount / globalThreeStore.totalModelsToLoad) * 100)}%)`
        );
      }
    }
  }

  private _updateSilentLoadingProgress(path: string, singleProgress: number): void {
    if (!this.silentLoading.active) return;
    this.silentLoading.modelProgress!.set(path, singleProgress);
    const totalProgress = Array.from(this.silentLoading.modelProgress!.values()).reduce((a, b) => a + b, 0);
    const avgProgress = Math.floor(totalProgress / this.silentLoading.totalModels);
    this.silentLoading.progress = avgProgress;

    // 发送单个模型进度事件给PPT预加载器
    window.dispatchEvent(
      new CustomEvent('silent-loading-progress', {
        detail: { path, progress: singleProgress / 100 },
      })
    );

    this._dispatchSilentLoadingEvent();
  }

  private _incrementSilentLoadedModels(): void {
    if (!this.silentLoading.active) return;
    this.silentLoading.loadedModels++;
    const overallProgress = Math.floor((this.silentLoading.loadedModels / this.silentLoading.totalModels) * 100);
    this.silentLoading.progress = overallProgress;
    this._dispatchSilentLoadingEvent();
    if (this.silentLoading.loadedModels >= this.silentLoading.totalModels) {
      setTimeout(() => {
        this.silentLoading.active = false;
        this._dispatchSilentLoadingEvent();
      }, 500);
    }
  }

  private _dispatchSilentLoadingEvent(): void {
    const event = new CustomEvent('silent-model-loading', {
      detail: {
        ...this.silentLoading,
      },
    });
    window.dispatchEvent(event);
  }

  findObjectByName(name: string): THREE.Object3D | null {
    if (!this.scene) return null;
    let foundObject: THREE.Object3D | null = null;
    this.scene.traverse((object: THREE.Object3D) => {
      if (object.name === name || object.name.includes(name)) {
        foundObject = object;
      }
    });
    if (!foundObject) {
      console.warn(`Object with name "${name}" not found in scene`);
    }
    return foundObject;
  }

  private _setupAnimationLoop(): void {
    if (this.animationLoop) return;
    const animate = () => {
      const now = Date.now() * 0.001;
      if (now - this.colorAnimState.lastUpdate > 0.02) {
        this.colorAnimState.lastUpdate = now;
        this.colorAnimState.hue += this.colorAnimState.speed * this.colorAnimState.direction;
        if (this.colorAnimState.hue >= 1) {
          this.colorAnimState.direction = -1;
        } else if (this.colorAnimState.hue <= 0) {
          this.colorAnimState.direction = 1;
        }
        this.colorAnimState.flow += this.colorAnimState.flowSpeed * this.colorAnimState.flowDirection;
        if (this.colorAnimState.flow >= 1) {
          this.colorAnimState.flow = 1;
          this.colorAnimState.flowDirection = -1;
        } else if (this.colorAnimState.flow <= 0) {
          this.colorAnimState.flow = 0;
          this.colorAnimState.flowDirection = 1;
        }
        const color = new THREE.Color();
        color.setHSL(this.colorAnimState.hue, 1.0, 0.8); // 更高的饱和度和亮度
        if (this.glowMaterial) {
          this.glowMaterial.color.copy(color);
          // MeshBasicMaterial 不需要 emissive 属性，颜色本身就会发光
        }
        // 更新RGB材质
        this._updateRGBMaterials(color);
        // 使用节流版本的更新函数
        this._updateCachedObjectsThrottled();
      }
      const frameId = requestAnimationFrame(animate);
      this.animationFrames.add(frameId);
      this.animationFrames.delete(frameId);
    };
    this.animationFrames.add(requestAnimationFrame(animate));
    this.animationLoop = true;
  }

  private _updateRGBMaterials(color: THREE.Color): void {
    if (this.rgbMaterials && this.rgbMaterials.size > 0) {
      this.rgbMaterials.forEach((material: THREE.Material) => {
        if (material instanceof THREE.MeshBasicMaterial) {
          material.color.copy(color);
          material.needsUpdate = true;
        } else if (material instanceof THREE.ShaderMaterial) {
          if (material.uniforms && material.uniforms.color) {
            material.uniforms.color.value.copy(color);
            material.uniformsNeedUpdate = true;
          }
          // 更新时间用于动画
          if (material.uniforms.time) {
            material.uniforms.time.value = Date.now() * 0.001;
          }
        }
      });
    }
  }

  private _trackRGBMaterial(material: THREE.Material): void {
    if (!this.rgbMaterials) {
      this.rgbMaterials = new Set<THREE.Material>();
    }
    this.rgbMaterials.add(material);
  }

  private _setupWeatherChangeListener(): void {
    this._weatherChangeListener = (_event: CustomEvent<{ weatherType: string }>) => {
      // 不再需要根据天气变化刷新建筑灯光
    };
  }
  private _refreshBuildingLights(): void {
    if (!this.scene) return;
    if (process.env.NODE_ENV === 'development') {
      console.log('[ModelLoaderManager] 开始刷新建筑灯光效果');
    }
    let refreshedLights = 0;

    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        if (
          child.name.includes('DengTiao') ||
          child.name.includes('Dengtiao') ||
          child.name.includes('dengtiao') ||
          child.name.includes('Wajing_DengTiao') ||
          child.name.includes('Curve')
        ) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] 正在处理灯带模型: ${child.name}`);
          }
          refreshedLights++;

          // 创建新的发光材质实例
          const material = new THREE.MeshBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: 1,
            side: THREE.DoubleSide,
            depthWrite: false,
            blending: THREE.AdditiveBlending,
          });

          // 保存原始材质
          if (!child.userData.originalMaterial) {
            child.userData.originalMaterial = child.material;
          }

          // 应用新材质
          child.material = material;
          child.visible = true;

          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] 已更新灯带材质: ${child.name}, 材质类型:`, material.type);
          }
        }
      }
    });

    // 强制更新场景
    if (this.scene.children.length > 0) {
      this.scene.children.forEach((child) => {
        if (child.type === 'Mesh') {
          child.matrixWorldNeedsUpdate = true;
        }
      });
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 建筑灯光刷新完成，共处理${refreshedLights}个灯光对象`);
    }

    // 如果没有找到任何灯带，输出警告
    if (refreshedLights === 0) {
      console.warn('[ModelLoaderManager] 警告：未找到任何灯带模型！请检查模型名称是否正确。');
    }

    this.animationLoop = false;
    this._setupAnimationLoop();
  }

  private _setupPlaceholderAnimationLoop(): void {
    const animate = () => {
      if (!this.placeholderAnimationEnabled) return;
      const currentTime = performance.now();
      const deltaTime = (currentTime - this.lastTime) / 1000;
      this.lastTime = currentTime;
      this.placeholderBoxes.forEach((box) => {
        updatePlaceholderAnimation(box, deltaTime);
      });
      this.placeholderAnimationId = requestAnimationFrame(animate);
    };
    this.placeholderAnimationId = requestAnimationFrame(animate);
  }

  private _createModelPlaceholder(modelPath: string, type: string): THREE.Group {
    if (this.placeholderBoxes.has(modelPath)) {
      this._removePlaceholder(modelPath);
    }
    this._playHologramSound();
    const placeholder = createDefaultPlaceholderBox(this.scene!, type);
    this.placeholderBoxes.set(modelPath, placeholder);
    if (this.dracoLoader) {
      // @ts-ignore
      this.dracoLoader._placeholderCallback = (min: THREE.Vector3, max: THREE.Vector3, size: THREE.Vector3) => {
        if (placeholder && min && max && size) {
          const center = new THREE.Vector3();
          center.addVectors(min, max).multiplyScalar(0.5);
          placeholder.position.copy(center);
          const mainBox = placeholder.children[0];
          if (mainBox instanceof THREE.Mesh && mainBox.geometry) {
            if (mainBox instanceof THREE.Mesh && mainBox.geometry instanceof THREE.BoxGeometry) {
              const scaleX = Math.max(1, size.x) / mainBox.geometry.parameters.width;
              const scaleY = Math.max(1, size.y) / mainBox.geometry.parameters.height;
              const scaleZ = Math.max(1, size.z) / mainBox.geometry.parameters.depth;
              placeholder.scale.set(scaleX, scaleY, scaleZ);
            }
          }
        }
      };
    }
    placeholder.scale.set(0.1, 0.1, 0.1);
    this._animatePlaceholderEntry(placeholder);
    return placeholder;
  }

  private _animatePlaceholderEntry(placeholder: THREE.Group): void {
    placeholder.children.forEach((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((mat: THREE.Material) => {
            (mat as THREE.MeshBasicMaterial).opacity = 0;
          });
        } else {
          (child.material as THREE.MeshBasicMaterial).opacity = 0;
        }
      }
    });
    const duration = 0.8;
    const finalScale = 1;
    const startTime = performance.now();
    const animate = () => {
      const now = performance.now();
      const elapsed = (now - startTime) / 1000;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutBack = (t: number) => {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
      };
      const easeValue = easeOutBack(progress);
      placeholder.scale.set(0.1 + (finalScale - 0.1) * easeValue, 0.1 + (finalScale - 0.1) * easeValue, 0.1 + (finalScale - 0.1) * easeValue);
      placeholder.children.forEach((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat: THREE.Material) => {
              (mat as THREE.MeshBasicMaterial).opacity = progress;
            });
          } else {
            (child.material as THREE.MeshBasicMaterial).opacity = progress;
          }
        }
      });
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  }

  private _playHologramSound(): void {
    if (process.env.NODE_ENV === 'development') {
      console.log('全息投影占位盒出现');
    }
  }

  private _removePlaceholder(modelPath: string): void {
    const placeholder = this.placeholderBoxes.get(modelPath);
    if (placeholder) {
      removePlaceholder(this.scene!, placeholder);
      this.placeholderBoxes.delete(modelPath);
    }
  }

  getCurrentFloorModel(): THREE.Object3D | undefined {
    return this.currentModels.find((model) => model.userData.type === 'floor');
  }

  public getCurrentModels(): (THREE.Scene | THREE.Group)[] {
    return this.currentModels;
  }

  public getScene(): THREE.Scene | null {
    return this.scene;
  }

  public resetAnimations(): void {
    if (this.cloudIconMaterial) {
      this.cloudIconMaterial.uniforms.time.value = 0;
    }
  }

  // 调试方法：手动检查场景中的所有对象
  public debugSceneObjects(): void {
    if (!this.scene) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[ModelLoaderManager] 场景未初始化');
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('[ModelLoaderManager] 开始调试场景对象...');
    }
    let meshCount = 0;
    let deviceMeshCount = 0;
    const lightObjects: string[] = [];
    const allObjects: string[] = [];
    const deviceObjects: string[] = [];
    const floorDeviceObjects: string[] = [];

    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        meshCount++;
        allObjects.push(child.name);

        // 检查是否为设备
        if (this._isDeviceMesh(child)) {
          deviceMeshCount++;
          deviceObjects.push(child.name);
        }

        // 检查是否为楼层设备
        if (isFloorDevice(child.name) || (child.parent && isFloorDevice(child.parent.name))) {
          floorDeviceObjects.push(child.name);
        }

        if (
          child.name.includes('Dengtiao') ||
          child.name.includes('dengtiao') ||
          child.name.includes('灯') ||
          child.name.includes('light') ||
          child.name.includes('Light') ||
          child.name.includes('lamp') ||
          child.name.includes('LED')
        ) {
          lightObjects.push(child.name);
          if (process.env.NODE_ENV === 'development') {
            console.log(`[ModelLoaderManager] 灯光对象: ${child.name}, userData:`, child.userData);
          }
        }
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 场景调试完成:`);
      console.log(`- 总网格数: ${meshCount}`);
      console.log(`- 设备网格数: ${deviceMeshCount}`);
      console.log(`- 楼层设备数: ${floorDeviceObjects.length}`);
      console.log(`- 灯光相关对象: ${lightObjects.length}个`);
      console.log(`- 当前可交互对象数: ${this.interactableObjects.length}`);
      console.log(`- 设备对象:`, deviceObjects);
      console.log(`- 楼层设备对象:`, floorDeviceObjects);
      console.log(`- 灯光对象:`, lightObjects);

      // 显示一些示例对象名称
      if (allObjects.length > 0) {
        console.log(`- 前10个对象名称:`, allObjects.slice(0, 10));
      }
    }
  }

  private _cacheSceneObjects(): void {
    if (!this.scene) return;
    this._cachedObjects = [];
    this.scene.traverse((object: THREE.Object3D) => {
      if (object instanceof THREE.Mesh) {
        this._cachedObjects.push(object);
      }
    });
  }

  private _updateCachedObjects(): void {
    this._cachedObjects.forEach((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const color = new THREE.Color();
        color.setHSL(this.colorAnimState.hue, 1.0, 0.8);

        // 特别处理灯带的发光效果
        if (object.name.includes('Dengtiao') || object.name.includes('dengtiao')) {
          if (object.userData.isRGBLight && (object.material as any).uniforms) {
            (object.material as any).uniforms.color.value.copy(color);
            (object.material as any).uniforms.intensity.value = 2.0; // 保持较高的发光强度
          }
        }

        // 更新云图标材质
        if (object.userData.isCloudIcon && (object.material as any).uniforms) {
          (object.material as any).uniforms.color.value.copy(color);
        }

        // 更新RGB发光材质
        if (object.userData.isRGBLight && (object.material as any).uniforms) {
          (object.material as any).uniforms.color.value.copy(color);
        } // 更新普通发光材质
        if (object.userData.isGlowLight) {
          (object.material as THREE.MeshBasicMaterial).color.copy(color);
        }
      }
    });
  }

  /**
   * 执行网格合并优化
   * @param model 要优化的模型
   * @param type 模型类型
   * @param modelPath 模型路径（用于预处理缓存）
   */
  private async _performMeshMerging(model: THREE.Object3D, type: string, modelPath?: string): Promise<void> {
    try {
      const meshMerger = MeshMerger.getInstance();

      // 配置合并选项
      const mergeOptions = {
        enabled: true,
        maxTriangles: type === 'floor' ? 30000 : 50000, // 楼层使用较小的三角面数限制
        mergeDifferentMaterials: false, // 保持材质一致性
        verbose: process.env.NODE_ENV === 'development',
      };

      // 如果有模型路径，尝试应用预处理的合并结果
      if (modelPath && meshMerger.applyPreprocessedMerging(modelPath, model)) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] ${type}模型使用预处理的网格合并`);
        }

        // 重新缓存场景对象，包含合并后的网格
        this._cacheSceneObjects();

        // 更新可交互对象数组，包含合并后的网格
        this._updateInteractableObjectsAfterMerging(type);
        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] 已更新interactableObjects，当前数量: ${this.interactableObjects.length}`);
        }

        // 通知ObjectSelection更新其可交互对象列表
        window.dispatchEvent(new CustomEvent('scene-objects-changed'));
        if (process.env.NODE_ENV === 'development') {
          console.log('[ModelLoaderManager] 已通知ObjectSelection更新可交互对象列表');
        }

        // 添加小延迟确保所有操作完成
        await new Promise((resolve) => setTimeout(resolve, 50));
        return;
      }

      // 执行常规合并
      const result = meshMerger.mergeMeshes(model, mergeOptions);

      if (result.mergedMeshCount > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] ${type}模型网格合并完成: ${result.originalMeshCount} -> ${result.mergedMeshCount} 网格`);
        }

        // 重新缓存场景对象，包含合并后的网格
        this._cacheSceneObjects();

        // 更新可交互对象数组，包含合并后的网格
        this._updateInteractableObjectsAfterMerging(type);

        // 通知ObjectSelection更新其可交互对象列表
        window.dispatchEvent(new CustomEvent('scene-objects-changed'));
      }

      // 添加小延迟确保所有操作完成
      await new Promise((resolve) => setTimeout(resolve, 50));
    } catch (error) {
      console.warn(`[ModelLoaderManager] ${type}模型网格合并失败:`, error);
    }
  }

  /**
   * 在网格合并后更新可交互对象数组
   * 确保包含合并后的网格，避免raycasting性能问题
   */
  private _updateInteractableObjectsAfterMerging(type: string): void {
    if (!this.scene) return;

    // 只有楼层模型需要更新可交互对象
    if (type !== 'floor') return;

    // 清空当前的可交互对象数组
    this.interactableObjects = [];

    // 重新收集所有可交互的mesh（包括合并后的mesh）
    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        // 检查是否是设备mesh或合并后的mesh
        const isDeviceMesh = this._isDeviceMesh(child);
        const isMergedMesh = child.userData.isMerged === true;

        // 设备mesh和合并后的mesh都应该被包含在可交互对象中
        if (isDeviceMesh || isMergedMesh) {
          this.interactableObjects.push(child);
        }
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(`[ModelLoaderManager] 网格合并后更新可交互对象: ${this.interactableObjects.length} 个对象`);
    }
  }

  /**
   * 判断mesh是否为设备mesh
   */
  private _isDeviceMesh(mesh: THREE.Mesh): boolean {
    // 检查是否为灯条
    const isDengtiao = this._isDengtiaoMesh(mesh);

    // 检查是否是4L模型（4L开头的模型不应该被识别为设备）
    const is4LModel = mesh.name.startsWith('4L') || (mesh.parent && mesh.parent.name.startsWith('4L'));

    // 使用deviceIdentifier中的逻辑判断是否为设备，但排除4L模型
    const isFloorDeviceMesh = isFloorDevice(mesh.name) || false;
    const isParentFloorDevice = mesh.parent ? isFloorDevice(mesh.parent.name) || false : false;

    return !is4LModel && (isDengtiao || isFloorDeviceMesh || isParentFloorDevice);
  }

  /**
   * 判断mesh是否为灯条
   */
  private _isDengtiaoMesh(mesh: THREE.Mesh): boolean {
    // 检查mesh自身名称
    if (mesh.name && mesh.name.toLowerCase().includes('dengtiao')) {
      return true;
    }

    // 检查父对象名称
    if (mesh.parent && mesh.parent.name && mesh.parent.name.toLowerCase().includes('dengtiao')) {
      return true;
    }

    // 检查祖父对象名称（可能有多层嵌套）
    let parent = mesh.parent;
    let depth = 0;
    while (parent && depth < 5) {
      // 限制检查深度，避免无限循环
      if (parent.name && parent.name.toLowerCase().includes('dengtiao')) {
        return true;
      }
      parent = parent.parent;
      depth++;
    }

    return false;
  }

  /**
   * PPT模式专用：快速切换当前楼层（无动画）
   */
  private async fastSwitchCurrentFloor(): Promise<void> {
    if (!this.scene || this.currentModels.length === 0) return;

    // 快速清理所有当前模型
    while (this.currentModels.length > 0) {
      const model = this.currentModels.pop();
      if (model) {
        // 从场景中移除模型
        this.scene.remove(model as THREE.Object3D);

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] PPT模式：快速移除模型 ${model.userData.type || 'unknown'}`);
        }
      }
    }

    // 清理parkModel引用（如果当前是外景模型）
    if (this.parkModel && !this.scene.children.includes(this.parkModel)) {
      this.parkModel = null;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('[ModelLoaderManager] PPT模式：快速清理当前模型完成');
    }
  }

  /**
   * PPT模式专用：快速显示楼层模型（无动画，跳过材质处理）
   */
  private async fastShowFloorModel(floorModel: THREE.Object3D): Promise<void> {
    if (!floorModel) return;

    // 直接显示，无动画，跳过材质遍历
    floorModel.visible = true;

    if (process.env.NODE_ENV === 'development') {
      console.log('[ModelLoaderManager] PPT模式：超快速显示楼层模型（跳过材质处理）');
    }
  }

  /**
   * PPT模式调试：检查当前场景状态
   */
  debugPPTSceneState(): void {
    if (process.env.NODE_ENV === 'development') {
      console.log('=== PPT场景状态调试 ===');
      console.log(`场景中的对象数量: ${this.scene?.children.length || 0}`);
      console.log(`currentModels数量: ${this.currentModels.length}`);
      console.log(`PPT实例缓存数量: ${this.pptModelInstances.size}`);

      if (this.scene) {
        const models = this.scene.children.filter((child) => child.userData.type === 'floor' || child.userData.type === 'exterior');
        console.log(`场景中的楼层/外景模型数量: ${models.length}`);
        models.forEach((model, index) => {
          console.log(`  模型${index + 1}: 类型=${model.userData.type}, 可见=${model.visible}, 名称=${model.name}`);
        });
      }

      console.log('=== 调试结束 ===');
    }
  }

  /**
   * PPT演示模式专用：全量预加载所有模型
   */
  async preloadAllModelsForPPT(): Promise<void> {
    try {
      console.log('[ModelLoaderManager] PPT模式全量预加载开始...');

      // 获取建筑数据
      const { buildingData } = await import('@/data/buildingData');

      // 收集所有需要预加载的模型路径
      const modelPaths: string[] = [];

      // 1. 外景模型
      if (buildingData.modelPath) {
        modelPaths.push(buildingData.modelPath);
        console.log('[ModelLoaderManager] 添加外景模型到预加载列表');
      }

      // 2. 所有楼层模型
      if (buildingData.floors) {
        buildingData.floors.forEach((floor: any) => {
          if (floor.modelPath) {
            modelPaths.push(floor.modelPath);
            console.log(`[ModelLoaderManager] 添加${floor.name}到预加载列表`);
          }
        });
      }

      console.log(`[ModelLoaderManager] 共发现 ${modelPaths.length} 个模型需要预加载`);

      // 发送预加载开始事件
      window.dispatchEvent(
        new CustomEvent('silent-loading-start', {
          detail: { modelPaths },
        })
      );

      // 使用现有的批量静默加载方法
      await this.batchSilentLoad(modelPaths);

      console.log('[ModelLoaderManager] PPT模式全量预加载完成！');
    } catch (error) {
      console.error('[ModelLoaderManager] PPT模式全量预加载失败:', error);
      throw error;
    }
  }

  /**
   * 为F1_LD_Ziti模型添加背光效果（和dengtiao相同的发光效果）
   */
  private _addTVBacklightEffect(model: THREE.Object3D): void {
    model.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh && child.name === 'F1_LD_Ziti') {
        // 使用和dengtiao相同的发光材质
        child.material = this.glowMaterial;
        child.userData.isGlowLight = true;
        child.visible = true;

        if (process.env.NODE_ENV === 'development') {
          console.log(`[ModelLoaderManager] 已为${child.name}添加背光效果`);
        }
      }
    });
  }
}
