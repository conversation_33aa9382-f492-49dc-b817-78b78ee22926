<template>
  <div :class="['w-full h-full p-[0.8vw] flex flex-col gap-[0.8vw]', customClass]">
    <!-- 运维工单 -->
    <div class="h-[calc((100%-2.4vw)/3)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">运维工单</div>
        <div
          class="absolute right-[1vw] top-[0.25vw] text-[0.6vw] text-blue-400 cursor-pointer hover:text-blue-300 transition-colors"
          @click="showSmartOperations"
          >智慧运维</div
        >
      </div>
      <WorkOrderPanel @locate-device="locateDevice" />
    </div>

    <!-- SLA预警 -->
    <div class="h-[calc((100%-2.4vw)/3)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">SLA预警</div>
      </div>
      <SlaAlertPanel @locate-device="locateDevice" />
    </div>

    <!-- 巡检任务 -->
    <div class="h-[calc((100%-2.4vw)/3)] relative flex flex-col">
      <div class="h-[1.6vw] shrink-0 relative">
        <img class="w-auto h-full absolute left-0 top-0" :src="dashboardTitle" alt="" />
        <div class="relative text-white absolute left-[1vw] top-[0.25vw] text-[0.7vw]">巡检任务</div>
        <div
          class="absolute right-[1vw] top-[0.25vw] text-[0.6vw] text-blue-400 cursor-pointer hover:text-blue-300 transition-colors"
          @click="showFullInspection"
          >完整功能</div
        >
      </div>
      <InspectionPanel />
    </div>
  </div>

  <FullInspectionPanel
    :visible="inspectionDialogVisible"
    @update:visible="inspectionDialogVisible = $event"
    :icon-src="dashboardTitle"
    :default-fullscreen="true"
  />

  <SmartOperationsModal
    :visible="smartOperationsDialogVisible"
    @update:visible="smartOperationsDialogVisible = $event"
    :icon-src="dashboardTitle"
    :default-fullscreen="true"
  />

  <div
    v-for="marker in alarmMarkers"
    :key="marker.deviceData.id"
    class="alarm-marker absolute z-1000 pointer-events-auto transition-transform duration-100 ease-out group"
    :style="{ transform: `translate(-50%, -100%) translate(${marker.screenX}px, ${marker.screenY}px)` }"
  >
    <!-- 故障标识图标 -->
    <div class="relative">
      <!-- 外圈动画 -->
      <div
        class="absolute -inset-2 rounded-full"
        :class="[
          marker.deviceData.faultLevel === 'critical'
            ? 'bg-red-500/20 animate-[ripple_1.5s_infinite]'
            : 'bg-yellow-500/20 animate-[ripple_2s_infinite]',
        ]"
      ></div>
      <!-- 主图标 -->
      <div
        class="w-8 h-8 rounded-full bg-black/80 backdrop-blur flex items-center justify-center cursor-pointer transition-all duration-200 hover:scale-110"
        :class="[marker.deviceData.faultLevel === 'critical' ? 'shadow-[0_0_20px_rgba(255,68,68,0.8)]' : 'shadow-[0_0_15px_rgba(255,204,0,0.7)]']"
      >
        <i class="fas fa-exclamation-triangle text-lg" :class="marker.deviceData.faultLevel === 'critical' ? 'text-red-500' : 'text-yellow-500'"></i>
      </div>
      <!-- 故障级别标签 -->
      <div
        class="absolute -right-2 -top-2 w-4 h-4 rounded-full text-[10px] flex items-center justify-center font-bold border-2"
        :class="[marker.deviceData.faultLevel === 'critical' ? 'bg-red-500 border-white text-white' : 'bg-yellow-500 border-white text-white']"
      >
        {{ marker.deviceData.faultLevel === 'critical' ? '!' : '?' }}
      </div>
    </div>

    <!-- 提示框 -->
    <div
      class="absolute top-[-5px] left-1/2 -translate-x-1/2 -translate-y-full opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none"
    >
      <div
        class="bg-[#001014]/95 backdrop-blur-sm border rounded-lg p-4 min-w-[200px] max-w-[300px] shadow-xl"
        :class="[marker.deviceData.faultLevel === 'critical' ? 'border-red-500/50' : 'border-yellow-500/50']"
      >
        <div class="text-white text-sm font-medium mb-2">{{ marker.deviceData.name }}</div>
        <div
          class="text-xs mb-1 inline-block px-2 py-0.5 rounded"
          :class="[marker.deviceData.faultLevel === 'critical' ? 'bg-red-500/20 text-red-400' : 'bg-yellow-500/20 text-yellow-400']"
        >
          {{ getFaultLevelText(marker.deviceData.faultLevel) }}
        </div>
        <div class="text-gray-300 text-xs mt-2">{{ marker.deviceData.faultDescription }}</div>
        <div class="text-gray-400 text-xs mt-2">
          <div>位置：{{ marker.deviceData.location }}</div>
          <div>时间：{{ marker.deviceData.faultTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, onActivated, onDeactivated, computed } from 'vue';
  import dashboardTitle from '@/assets/scene/dashboardTitle.png';
  import { ModelLoaderManager } from '../../../lib/load/ModelLoaderManager';
  import { ObjectSelection } from '../../../lib/selection/ObjectSelection';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
  import { SceneManager } from '../../../lib/SceneManager';
  import * as THREE from 'three';
  import WorkOrderPanel from './components/WorkOrderPanel.vue';
  import SlaAlertPanel from './components/SlaAlertPanel.vue';
  import InspectionPanel from './components/InspectionPanel.vue';
  import FullInspectionPanel from './components/FullInspectionPanel.vue';
  import SmartOperationsModal from './components/SmartOperationsModal.vue';

  const props = defineProps({
    class: {
      type: String,
      default: '',
    },
  });

  // 将props.class重命名为customClass以避免与HTML class属性冲突
  const customClass = computed(() => props.class);

  const inspectionDialogVisible = ref(false);
  const smartOperationsDialogVisible = ref(false);
  const globalThreeStore = useGlobalThreeStore();
  const faultyDevices = ref([
    {
      id: 1,
      name: '1f_device_104',
      location: '1F-A区-104号位',
      faultLevel: 'critical',
      faultDescription: '温度过高，需要立即处理',
      faultTime: '2024-02-28 10:30',
    },
    {
      id: 2,
      name: 'UPS-A01',
      location: '1F-B区-02号位',
      faultLevel: 'warning',
      faultDescription: '电池电量低，建议更换',
      faultTime: '2024-02-28 09:15',
    },
  ]);
  const alarmMarkers = ref([]);
  let raycaster = null;
  let mouse = null;
  const isComponentActive = ref(false);

  const showFullInspection = () => {
    console.log('Opening full inspection panel...');
    inspectionDialogVisible.value = true;
  };

  const showSmartOperations = () => {
    console.log('Opening smart operations panel...');
    smartOperationsDialogVisible.value = true;
  };

  onMounted(async () => {
    isComponentActive.value = true;

    // 减少延迟时间，从1000ms减少到300ms
    setTimeout(() => {
      initializeAlarmEffects();
      addMouseInteraction();
    }, 300);

    // 监听楼层切换事件
    globalThreeStore.$subscribe((_, state) => {
      if (state.currentFloorId && isComponentActive.value) {
        // 当楼层变化且组件处于激活状态时，重新初始化告警效果
        clearAlarmEffects();
        setTimeout(() => {
          initializeAlarmEffects();
        }, 200); // 从800ms减少到200ms
      }
    });
  });

  onActivated(() => {
    isComponentActive.value = true;

    // 减少组件激活后的延时，从500ms减少到50ms
    // 同时添加视觉提示，让用户感知加载过程
    showLoadingHint();
    setTimeout(() => {
      initializeAlarmEffects();
      addMouseInteraction();
      hideLoadingHint();
    }, 50);
  });

  onDeactivated(() => {
    isComponentActive.value = false;
    // 组件停用时清理告警效果
    clearAlarmEffects();
    removeMouseInteraction();
  });

  onUnmounted(() => {
    isComponentActive.value = false;
    clearAlarmEffects();
    removeMouseInteraction();
  });

  // 添加简单的加载提示函数
  const showLoadingHint = () => {
    const existingHint = document.getElementById('maintenance-loading-hint');
    if (existingHint) return;

    const hint = document.createElement('div');
    hint.id = 'maintenance-loading-hint';
    hint.style.cssText = `
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 9999;
    transition: opacity 0.3s ease;
  `;
    hint.textContent = '准备故障设备信息...';
    document.body.appendChild(hint);
  };

  const hideLoadingHint = () => {
    const hint = document.getElementById('maintenance-loading-hint');
    if (hint) {
      hint.style.opacity = '0';
      setTimeout(() => {
        if (hint.parentNode) hint.parentNode.removeChild(hint);
      }, 300);
    }
  };

  // 初始化告警效果
  const initializeAlarmEffects = () => {
    // 如果组件不处于激活状态，不执行初始化
    if (!isComponentActive.value) return;

    const objectSelection = ObjectSelection.getInstance();

    if (!objectSelection?.objectHighlighter) {
      console.warn('无法获取对象高亮器');
      return;
    }

    // 预先准备高亮配置
    const warningConfig = {
      color: 0xff0000,
      intensity: 1.8,
      pulseEnabled: true,
      outlineScale: 1.03,
      outlineOpacity: 0.7,
      useHalo: false,
      multiOutline: false,
      warningPulse: true,
    };

    // 直接设置配置，避免条件检查
    objectSelection.objectHighlighter.highlightConfig.warning = warningConfig;

    // 使用Promise.all并行处理所有设备高亮
    Promise.all(
      faultyDevices.value.map(async (device) => {
        try {
          // 解析设备楼层和名称
          const floorMatch = device.location.match(/(\d+)F/);
          if (!floorMatch) return;

          const floorNumber = parseInt(floorMatch[1]);
          const currentFloorId = globalThreeStore.currentFloorId;

          // 只处理当前楼层的设备
          if (!currentFloorId || !currentFloorId.includes(floorNumber.toString())) {
            return;
          }

          // 尝试查找设备对象
          const deviceObj = findDeviceObject(device.name);
          if (!deviceObj) {
            return;
          }

          if (deviceObj) {
            // 强制移除可能存在的其他高亮效果
            objectSelection.objectHighlighter.forceUnhighlightObject(deviceObj);

            // 应用告警高亮效果
            objectSelection.objectHighlighter.highlightGroup(deviceObj, 'warning');

            // 添加告警标记
            addAlarmMarker(deviceObj, device);
          }
        } catch (err) {
          console.error(`为设备 ${device.name} 添加告警效果失败:`, err);
        }
      })
    );
  };

  // 清除所有告警效果
  const clearAlarmEffects = () => {
    // 移除场景中的告警标记
    alarmMarkers.value.forEach((marker) => {
      if (marker.element && marker.element.parentNode) {
        marker.element.parentNode.removeChild(marker.element);
      }
    });
    alarmMarkers.value = [];

    // 使用 clearAllHighlights 方法清除所有高亮效果
    const objectSelection = ObjectSelection.getInstance();
    if (objectSelection?.objectHighlighter) {
      objectSelection.objectHighlighter.clearAllHighlights();
    }
  };

  // 查找设备对象的辅助方法
  const findDeviceObject = (deviceName) => {
    const modelLoaderManager = ModelLoaderManager.getInstance();

    // 尝试不同格式的设备名称
    const nameVariants = [
      deviceName,
      deviceName.toLowerCase(),
      deviceName.replace(/_/g, ''),
      `device_${deviceName.split('_').pop()}`,
      // 如果是 1f_device_104 格式，尝试 device_104
      deviceName.includes('_device_') ? `device_${deviceName.split('_device_')[1]}` : null,
    ].filter(Boolean);

    // 尝试每一种可能的名称
    for (const name of nameVariants) {
      const obj = modelLoaderManager.findObjectByName(name);
      if (obj) return obj;
    }

    return null;
  };

  // 定位设备方法 - 由子组件触发
  const locateDevice = async (deviceName) => {
    try {
      console.log('开始定位设备:', deviceName);

      // 显示加载提示
      showLoadingMessage(deviceName);

      const modelLoaderManager = ModelLoaderManager.getInstance();
      const objectSelection = ObjectSelection.getInstance();

      if (!modelLoaderManager || !objectSelection) {
        showErrorMessage('无法获取场景管理器');
        return;
      }

      // 解析设备楼层和名称 - 支持F1-F6格式和数字f格式
      let floorNumber = null;

      // 首先尝试F1-F6格式（如F1、F2等）
      const floorMatchF = deviceName.match(/F(\d+)/i);
      if (floorMatchF) {
        floorNumber = parseInt(floorMatchF[1]);
      } else {
        // 然后尝试数字f格式（如1f、2f等）
        const floorMatchNum = deviceName.match(/(\d+)f/i);
        if (floorMatchNum) {
          floorNumber = parseInt(floorMatchNum[1]);
        }
      }

      if (!floorNumber) {
        console.warn('无法解析楼层信息:', deviceName);
        showErrorMessage('无法解析设备楼层信息');
        return;
      }

      const targetFloorId = `${floorNumber}F`; // 目标楼层ID - 使用buildingData中的格式

      // 获取当前楼层ID
      const currentFloorId = globalThreeStore.currentFloorId;
      console.log('当前楼层:', currentFloorId, '目标楼层:', targetFloorId);

      // 只有当前楼层与目标楼层不同时才切换模型
      if (currentFloorId !== targetFloorId) {
        console.log('需要切换楼层');

        try {
          await globalThreeStore.switchFloor(targetFloorId);
          // 等待模型加载完成
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (error) {
          console.error('楼层切换失败:', error);
          showErrorMessage('楼层切换失败');
          return;
        }
      }

      // 尝试查找设备对象
      const deviceObj = findDeviceObject(deviceName);
      if (!deviceObj) {
        console.warn('未找到设备对象:', deviceName);
        showErrorMessage(`未找到设备: ${deviceName}`);
        return;
      }

      console.log('找到设备对象:', deviceObj.name);

      // 高亮并定位设备
      if (objectSelection?.objectDoubleClickHandler) {
        // 设置观察模式状态
        objectSelection.objectDoubleClickHandler.isDeviceObserving = true;

        // 应用高亮效果
        if (objectSelection.objectHighlighter) {
          const highlighter = objectSelection.objectHighlighter;

          // 确保配置正确
          if (!highlighter.highlightConfig.warning) {
            highlighter.highlightConfig.warning = {
              color: 0xff0000,
              intensity: 2.5,
              pulseEnabled: true,
              outlineScale: 1.03,
              outlineOpacity: 0.7,
              useHalo: false,
              multiOutline: false,
              warningPulse: true,
            };
          }

          // 高亮设备
          highlighter.highlightGroup(deviceObj, 'warning');
        }

        // 执行设备观察
        objectSelection.objectDoubleClickHandler.handleObjectDoubleSelection(deviceObj);

        // 显示成功消息
        showSuccessMessage(`已定位到设备: ${deviceName}`);
      }
    } catch (error) {
      console.error('设备定位失败:', error);
      showErrorMessage('设备定位失败: ' + error.message);
    }
  };

  // 添加告警标记
  const addAlarmMarker = (deviceObj, deviceData) => {
    if (!deviceObj) return;

    const sceneManager = SceneManager.getInstance();
    if (!sceneManager || !sceneManager.camera) return;

    // 创建标记DOM元素
    const marker = document.createElement('div');
    marker.className = 'alarm-marker';
    marker.dataset.deviceId = deviceData.id;
    marker.innerHTML = `
    <div class="alarm-icon ${deviceData.faultLevel === 'critical' ? 'critical' : 'warning'}">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    <div class="alarm-tooltip">
      <div class="alarm-title">${deviceData.name}</div>
      <div class="alarm-level ${deviceData.faultLevel}">${getFaultLevelText(deviceData.faultLevel)}</div>
      <div class="alarm-description">${deviceData.faultDescription}</div>
      <div class="alarm-time">${deviceData.faultTime}</div>
      <div class="alarm-location">${deviceData.location}</div>
    </div>
  `;
    document.body.appendChild(marker);

    // 设置标记位置
    const position = new THREE.Vector3();
    deviceObj.getWorldPosition(position);

    // 添加到标记列表
    alarmMarkers.value.push({
      element: marker,
      object: deviceObj,
      worldPosition: position.clone(),
      deviceData,
    });

    // 更新标记位置
    updateMarkerPosition(marker, position, sceneManager.camera);
  };

  // 更新标记位置
  const updateMarkerPosition = (marker, worldPosition, camera) => {
    // 将3D世界坐标转换为屏幕坐标
    const vector = worldPosition.clone();
    vector.project(camera);

    const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
    const y = (-vector.y * 0.5 + 0.5) * window.innerHeight;

    marker.style.transform = `translate(-50%, -100%) translate(${x}px, ${y}px)`;
    marker.style.display = vector.z < 1 ? 'block' : 'none';
  };

  // 添加鼠标交互
  const addMouseInteraction = () => {
    try {
      raycaster = new THREE.Raycaster();
      mouse = new THREE.Vector2();

      // 添加场景更新回调，更新标记位置
      const sceneManager = SceneManager.getInstance();
      if (sceneManager && typeof sceneManager.addUpdateCallback === 'function') {
        sceneManager.addUpdateCallback(updateAllMarkers);
      } else {
        console.warn('SceneManager addUpdateCallback not available');
      }

      // 添加鼠标移动事件
      window.addEventListener('mousemove', handleMouseMove);
    } catch (error) {
      console.error('Error in addMouseInteraction:', error);
    }
  };

  // 移除鼠标交互
  const removeMouseInteraction = () => {
    try {
      const sceneManager = SceneManager.getInstance();
      if (sceneManager && typeof sceneManager.removeUpdateCallback === 'function') {
        sceneManager.removeUpdateCallback(updateAllMarkers);
      }

      window.removeEventListener('mousemove', handleMouseMove);
    } catch (error) {
      console.error('Error in removeMouseInteraction:', error);
    }
  };

  // 更新所有标记的位置
  const updateAllMarkers = () => {
    const sceneManager = SceneManager.getInstance();
    if (!sceneManager || !sceneManager.camera) return;

    alarmMarkers.value.forEach((marker) => {
      if (marker.element && marker.object) {
        // 检查设备是否可见
        let isVisible = true;
        marker.object.traverseAncestors((ancestor) => {
          if (ancestor.visible === false) {
            isVisible = false;
          }
        });

        if (!isVisible || !marker.object.visible) {
          marker.element.style.display = 'none';
          return;
        }

        // 更新位置
        updateMarkerPosition(marker.element, marker.worldPosition, sceneManager.camera);
      }
    });
  };

  // 处理鼠标移动事件
  const handleMouseMove = (event) => {
    const sceneManager = SceneManager.getInstance();
    if (!sceneManager || !sceneManager.camera || !raycaster || !mouse) return;

    // 计算鼠标在归一化设备坐标中的位置
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // 创建射线
    raycaster.setFromCamera(mouse, sceneManager.camera);

    // 获取所有故障设备对象
    const faultyDeviceObjects = alarmMarkers.value.map((marker) => marker.object).filter(Boolean);

    // 检查射线与哪些设备相交
    const intersects = raycaster.intersectObjects(faultyDeviceObjects, true);

    if (intersects.length > 0) {
      // 找到相交的第一个设备
      const intersectedObj = intersects[0].object;

      // 查找包含此对象的设备
      const marker = alarmMarkers.value.find((m) => {
        if (!m.object) return false;

        // 检查是否是同一对象或其子对象
        let isChild = false;
        intersectedObj.traverseAncestors((ancestor) => {
          if (ancestor === m.object) isChild = true;
        });

        return m.object === intersectedObj || isChild;
      });

      // 显示提示
      if (marker && marker.element) {
        marker.element.classList.add('hover');
      }

      // 隐藏其他提示
      alarmMarkers.value.forEach((m) => {
        if (m !== marker && m.element) {
          m.element.classList.remove('hover');
        }
      });
    } else {
      // 当鼠标没有悬停在任何设备上时，隐藏所有提示
      alarmMarkers.value.forEach((m) => {
        if (m.element) {
          m.element.classList.remove('hover');
        }
      });
    }
  };

  // 获取故障级别文本
  const getFaultLevelText = (level) => {
    switch (level) {
      case 'critical':
        return '严重故障';
      case 'warning':
        return '警告';
      default:
        return '未知';
    }
  };

  // 消息提示方法
  const showLoadingMessage = (deviceName) => {
    if (window.$message) {
      window.$message.loading({
        content: `正在定位设备: ${deviceName}...`,
        duration: 0,
        key: 'locate-device',
      });
    }
  };

  const showSuccessMessage = (message) => {
    if (window.$message) {
      window.$message.success({
        content: message,
        duration: 3,
        key: 'locate-device',
      });
    }
  };

  const showErrorMessage = (message) => {
    if (window.$message) {
      window.$message.error({
        content: message,
        duration: 3,
        key: 'locate-device',
      });
    }
  };
</script>

<style scoped>
  @keyframes ripple {
    0% {
      transform: scale(0.8);
      opacity: 0.8;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }

  .alarm-marker {
    will-change: transform;
  }
</style>
