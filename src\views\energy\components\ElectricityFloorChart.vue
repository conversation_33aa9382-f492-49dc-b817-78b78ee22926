<template>
  <a-row>
    <a-col :span="24">
      <div class="relative w-full">
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center gap-4">
            <span class="text-base">楼层电耗统计</span>
          </div>
          <a-button type="primary" size="small" @click="handleRefresh" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center" style="height: 50vh">
          <a-spin size="large" />
        </div>

        <!-- 图表容器 -->
        <div ref="chartRef" style="width: 100%; height: 50vh"></div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import { debounce } from 'lodash-es';
  import { message } from 'ant-design-vue';
  import * as echarts from 'echarts';
  import { getFloorEl } from '/@/api/electricityMeter';
  import { ReloadOutlined } from '@ant-design/icons-vue';

  const loading = ref(false);
  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  // 楼层电耗数据
  const floorElectricityData = ref<any[]>([]);

  // 初始化图表
  const initChart = async () => {
    await nextTick();
    if (chartRef.value) {
      chartInstance = echarts.init(chartRef.value);
      window.addEventListener('resize', handleResize);
    }
  };

  // 渲染图表
  const renderChart = () => {
    if (!chartInstance) {
      return;
    }

    if (!floorElectricityData.value.length) {
      // 显示无数据状态
      chartInstance.clear();
      chartInstance.setOption({
        title: {
          text: '楼层电耗统计',
          left: 'center',
          textStyle: {
            color: '#2c3e50',
            fontWeight: 'bold',
            fontSize: 16,
          },
        },
        xAxis: { show: false },
        yAxis: { show: false },
        series: [],
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: '暂无楼层电耗数据',
                fontSize: 16,
                fill: '#999',
              },
            },
          ],
        },
      });
      return;
    }

    // 按楼层排序数据
    const sortedData = [...floorElectricityData.value].sort((a, b) => {
      const floorA = parseInt(a.floorInfo) || 0;
      const floorB = parseInt(b.floorInfo) || 0;
      return floorA - floorB;
    });

    const categories = sortedData.map((item) => `${item.floorInfo}层`);
    const values = sortedData.map((item) => parseFloat(item.valueData) || 0);
    const minVal = Math.min(...values, 0);
    const maxVal = Math.max(...values, 0);

    const option = {
      title: {
        text: '楼层电耗统计',
        left: 'center',
        textStyle: {
          color: '#2c3e50',
          fontWeight: 'bold',
          fontSize: 16,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 0,
          margin: 10,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '电耗(kW)',
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      visualMap: {
        show: false,
        min: minVal,
        max: maxVal,
        inRange: {
          color: ['#3B8EE6', '#77A9E3', '#A8C8F0'],
        },
      },
      series: [
        {
          name: '楼层电耗',
          type: 'bar',
          data: values,
          barWidth: '60%',
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 8,
            shadowOffsetY: 3,
          },
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 11,
            fontWeight: 'bold',
            formatter: (params: any) => {
              const value = parseFloat(params.value);
              return `${value.toLocaleString()} kW`;
            },
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 12,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              borderColor: '#555',
              borderWidth: 1,
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: (params: any) => {
          const data = params[0];
          const value = parseFloat(data.value);
          return `${data.name}<br/>电耗: ${value.toLocaleString()} kW`;
        },
      },
    };

    chartInstance.setOption(option);
  };

  // 加载楼层电耗数据
  const loadFloorElectricityData = async () => {
    try {
      loading.value = true;

      const result = await getFloorEl();

      // result 本身就是数据数组
      let apiData = [];
      if (Array.isArray(result)) {
        apiData = result;
      } else {
        console.warn('API返回数据格式不正确:', result);
      }

      // 验证数据格式
      if (Array.isArray(apiData) && apiData.length > 0) {
        floorElectricityData.value = apiData;
        renderChart();
      } else {
        console.warn('API返回的数据为空或格式不正确');
        message.warning('暂无楼层电耗数据');

        // 如果API没有数据，使用模拟数据作为后备
        floorElectricityData.value = [
          {
            valueData: 183224.0,
            floorInfo: '1',
            describe: 'KW',
          },
          {
            valueData: 95600.0,
            floorInfo: '2',
            describe: 'KW',
          },
          {
            valueData: 41504.0,
            floorInfo: '3',
            describe: 'KW',
          },
          {
            valueData: 67800.0,
            floorInfo: '4',
            describe: 'KW',
          },
        ];
        renderChart();
      }
    } catch (error) {
      console.error('加载楼层电耗数据失败:', error);
      message.error('加载楼层电耗数据失败');
      floorElectricityData.value = [];
      renderChart();
    } finally {
      loading.value = false;
    }
  };

  const handleResize = debounce(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 300);

  const handleRefresh = () => {
    if (!loading.value) {
      loadFloorElectricityData();
    }
  };

  onMounted(() => {
    initChart();
    loadFloorElectricityData();
  });

  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .ant-empty {
    margin: 50px 0;
  }
</style>
