<template>
  <div class="absolute z-1 w-full h-full left-0 top-0 flex flex-col pointer-events-none">
    <!-- Header, Sidebars, and other components remain the same when not in PPT mode -->
    <template v-if="!pptDemonstrationActive">
      <div class="h-[3.4vw] w-full absolute left-0 top-0 pointer-events-auto z-10">
        <DataCenterHeader />
      </div>
      <!-- 左边栏 -->
      <div
        class="absolute left-0 top-[3.4vw] h-[calc(100%-6.4vw)] pointer-events-auto transition-all duration-300 bg-[rgba(21,28,45,0.7)] rounded-r-lg bg-gradient-to-r from-[#020a399e] to-transparent"
        :style="{ width: leftBarWidth }"
      >
        <img :src="leftBarShowBtn" alt="leftBarShowBtn" class="w-[1.4vw] h-full absolute left-0 top-0 cursor-pointer" @click="toggleLeftBar" />
        <div class="mt-[0.6vw] overflow-hidden whitespace-nowrap h-[calc(100%-0.6vw)] pl-4">
          <component
            :is="currentLeftComponent"
            :key="globalThreeStore.isDeviceDetailActive ? `device-detail-left-${globalThreeStore.currentDeviceCode}` : `left-${activeMenu}`"
            :class="{ 'opacity-0': leftBarWidth === '1.4vw', 'opacity-100': leftBarWidth === '20vw' }"
            class="transition-opacity duration-300"
          />
        </div>
      </div>
      <!-- 右边栏 -->
      <div
        class="absolute right-0 top-[3.4vw] h-[calc(100%-6.4vw)] pointer-events-auto transition-all duration-300 bg-[rgba(21,28,45,0.7)] rounded-l-lg bg-gradient-to-l from-[#020a399e] to-transparent"
        :style="{ width: rightBarWidth }"
      >
        <div class="absolute right-0 top-0 h-full w-[1.4vw] cursor-pointer z-10" @click="toggleRightBar">
          <img :src="rightBarShowBtn" alt="rightBarShowBtn" class="w-[1.4vw] h-full absolute right-0 top-0" />
        </div>
        <div class="mt-[0.6vw] overflow-hidden whitespace-nowrap h-[calc(100%-0.6vw)] pr-[1.4vw]">
          <component
            :is="currentRightComponent"
            :key="globalThreeStore.isDeviceDetailActive ? `device-detail-right-${globalThreeStore.currentDeviceCode}` : `right-${activeMenu}`"
            :class="{ 'opacity-0': rightBarWidth === '1.4vw', 'opacity-100': rightBarWidth === '20vw' }"
            class="transition-opacity duration-300"
          />
        </div>
      </div>
      <!-- ViewControl 组件 -->
      <div
        class="absolute w-[4vw] h-[18vw] top-[4vw] transition-all duration-300 pointer-events-auto flex justify-center"
        :style="{ right: rightBarWidth }"
      >
        <ViewControl />
      </div>
      <!-- 楼层选择 -->
      <div
        class="absolute w-[5vw] h-[24vw] top-[5vw] transition-all duration-300 pointer-events-auto flex justify-center"
        :style="{ left: leftBarWidth }"
      >
        <FloorMenu />
      </div>
    </template>

    <!-- 底部导航栏 -->
    <BottomNavigation v-if="!pptDemonstrationActive" :menuList="menuList" :activeMenu="activeMenu" @menu-select="selectMenu" />

    <!-- 评测服务弹窗 -->
    <EvaluationDialog v-model="evaluationDialogVisible" />

    <!-- PPT预加载组件 -->
    <PPTPreloader :visible="pptPreloadingActive" @complete="onPPTPreloadComplete" />

    <!-- PPT演示组件 -->
    <NewPPTDemonstration v-if="pptDemonstrationActive" ppt-path="docs/副本DCIM平台介绍0527.pptx" @exit="exitPPTDemonstration" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onBeforeUnmount, onActivated, onDeactivated, watch } from 'vue';
  import type { Component } from 'vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import DataCenterHeader from '../../components/DataCenterHeader.vue';
  import leftBarShowBtn from '/@/assets/scene/leftBarShowBtn.png';
  import rightBarShowBtn from '/@/assets/scene/rightBarShowBtn.png';
  import FloorMenu from '/@/views/scene/components/FloorMenu.vue';
  import BottomNavigation from '../../components/BottomNavigation.vue';
  import ViewControl from '../../components/ViewControl.vue';
  import NewPPTDemonstration from '../../components/ppt/NewPPTDemonstration.vue';
  import PPTPreloader from '../../components/ppt/PPTPreloader.vue';

  // 导入菜单组件
  import OperationsLeft from '../components/operations/OperationsLeft.vue';
  import OperationsRight from '../components/operations/OperationsRight.vue';
  import SafetyLeft from '../components/safety/SafetyLeft.vue';
  import SafetyRight from '../components/safety/SafetyRight.vue';
  import MaintenanceLeft from '../components/maintenance/MaintenanceLeft.vue';
  import MaintenanceRight from '../components/maintenance/MaintenanceRight.vue';
  import CarbonLeft from '../components/carbon/CarbonLeft.vue';
  import CarbonRight from '../components/carbon/CarbonRight.vue';
  import FireSafetyLeft from '../components/fireSafety/FireSafetyLeft.vue';
  import FireSafetyRight from '../components/fireSafety/FireSafetyRight.vue';
  import EnergyLeft from '../components/energy/EnergyLeft.vue';
  import EnergyRight from '../components/energy/EnergyRight.vue';
  import AssetLeft from '../components/asset/AssetLeft.vue';
  import AssetRight from '../components/asset/AssetRight.vue';
  import ProjectLeft from '../components/project/ProjectLeft.vue';
  import ProjectRight from '../components/project/ProjectRight.vue';
  import BimLeft from '../components/bim/BimLeft.vue';
  import BimRight from '../components/bim/BimRight.vue';
  import EvaluationDialog from '../components/evaluation/EvaluationDialog.vue';
  import CloudLeft from '../components/cloud/CloudLeft.vue';
  import CloudRight from '../components/cloud/CloudRight.vue';

  // 导入设备详情组件
  import DeviceDetailLeft from '../components/deviceDetail/DeviceDetailLeft.vue';
  import DeviceDetailRight from '../components/deviceDetail/DeviceDetailRight.vue';

  // 接口定义
  interface MenuComponents {
    [key: string]: {
      left: Component | null;
      right: Component | null;
    };
  }

  interface MenuItem {
    title: string;
  }

  // 菜单组件映射表
  const menuComponents: MenuComponents = {
    智慧运维: { left: MaintenanceLeft, right: MaintenanceRight },
    智慧安消防: { left: FireSafetyLeft, right: FireSafetyRight },
    低碳节能: { left: CarbonLeft, right: CarbonRight },
    智慧告警: { left: SafetyLeft, right: SafetyRight },
    智慧能耗: { left: EnergyLeft, right: EnergyRight },
    运营数据管理: { left: OperationsLeft, right: OperationsRight },
    资产可视化管理: { left: AssetLeft, right: AssetRight },
    综合管理: { left: ProjectLeft, right: ProjectRight },
    BIM技术应用: { left: BimLeft, right: BimRight },
    存储和部署管理: { left: CloudLeft, right: CloudRight },
    评测服务: { left: null, right: null },
  };

  // 获取全局状态
  const globalThreeStore = useGlobalThreeStore();

  // 状态定义
  const activeMenu = ref<string>('智慧安消防');
  const leftBarWidth = ref<string>('20vw');
  const rightBarWidth = ref<string>('20vw');
  const evaluationDialogVisible = ref<boolean>(false);
  const pptDemonstrationActive = ref<boolean>(false);
  const pptPreloadingActive = ref<boolean>(false);

  // 计算属性
  const currentLeftComponent = computed<Component | null>(() => {
    // 如果设备详情激活，显示设备详情左侧组件
    if (globalThreeStore.isDeviceDetailActive) {
      return DeviceDetailLeft;
    }
    // 否则显示当前菜单对应的左侧组件
    return menuComponents[activeMenu.value]?.left || null;
  });

  const currentRightComponent = computed<Component | null>(() => {
    // 如果设备详情激活，显示设备详情右侧组件
    if (globalThreeStore.isDeviceDetailActive) {
      return DeviceDetailRight;
    }
    // 否则显示当前菜单对应的右侧组件
    return menuComponents[activeMenu.value]?.right || null;
  });

  // 方法定义
  const toggleLeftBar = (): void => {
    leftBarWidth.value = leftBarWidth.value === '20vw' ? '1.4vw' : '20vw';
  };

  const toggleRightBar = (): void => {
    rightBarWidth.value = rightBarWidth.value === '20vw' ? '1.4vw' : '20vw';
  };

  // 菜单列表
  const menuList: MenuItem[] = [
    { title: '智慧安消防' },
    { title: '智慧能耗' },
    { title: '智慧运维' },
    { title: '智慧告警' },
    { title: '运营数据管理' },
    { title: '资产可视化管理' },
    { title: '综合管理' },
    { title: 'BIM技术应用' },
    { title: '存储和部署管理' },
    { title: '评测服务' },
    { title: 'PPT演示' },
  ];

  // 菜单选择处理
  const selectMenu = (title: string): void => {
    if (title === '评测服务') {
      evaluationDialogVisible.value = true;
      return;
    }

    if (title === 'PPT演示') {
      // 检查是否在内景模式
      if (globalThreeStore.currentView !== 2) {
        if (window.$message) {
          window.$message.warning('PPT演示功能仅在内景模式下可用，请先切换到内景模式');
        }
        return;
      }

      // 启动PPT演示
      startPPTDemonstration();
      return;
    }

    if (activeMenu.value === title) return;

    const oldMenu = activeMenu.value;
    console.log(`从 ${oldMenu} 切换到: ${title}`);

    try {
      clearMaintenanceEffects();
    } catch (error) {
      console.warn('清理旧菜单效果时出错:', error);
    }

    activeMenu.value = title;
  };

  // PPT演示相关方法
  const startPPTDemonstration = async (): Promise<void> => {
    console.log('开始启动PPT演示模式 - 显示预加载界面');

    // 显示预加载界面
    pptPreloadingActive.value = true;

    // 开始预加载（不等待完成，让PPTPreloader组件控制流程）
    performPPTPreloading();
  };

  // PPT预加载完成回调
  const onPPTPreloadComplete = (): void => {
    console.log('PPT预加载完成，启动演示模式');

    // 隐藏预加载界面
    pptPreloadingActive.value = false;

    // 启动PPT演示
    pptDemonstrationActive.value = true;

    // 设置全局状态
    globalThreeStore.setPPTDemonstrationActive(true);
  };

  // PPT演示预加载机制
  const performPPTPreloading = async (): Promise<void> => {
    try {
      console.log('[PPT预加载] 开始全量预加载所有模型...');

      // 获取模型管理器
      const { ModelLoaderManager } = await import('../../lib/load/ModelLoaderManager');
      const modelLoader = ModelLoaderManager.getInstance();

      // 使用专用的全量预加载方法（不等待完成，让PPTPreloader组件处理进度）
      modelLoader?.preloadAllModelsForPPT();

      console.log('[PPT预加载] 预加载已启动，等待PPTPreloader组件完成...');
    } catch (error) {
      console.error('[PPT预加载] 预加载过程出错:', error);
    }
  };

  const exitPPTDemonstration = (): void => {
    console.log('退出PPT演示模式');
    pptDemonstrationActive.value = false;

    // 清理全局状态
    globalThreeStore.setPPTDemonstrationActive(false);
  };

  // 清理智慧运维效果
  const clearMaintenanceEffects = (): void => {
    console.log('清除智慧运维模块的效果...');
    try {
      const maintenanceComponents = menuComponents['智慧运维'];
      if (!maintenanceComponents) {
        console.log('未找到智慧运维组件');
        return;
      }

      // 安全地访问和清理左侧组件
      const leftComponent = maintenanceComponents.left as any;
      if (leftComponent?.$el && typeof leftComponent.clearAlarmEffectTimers === 'function') {
        leftComponent.clearAlarmEffectTimers();
      }

      // 安全地访问和清理右侧组件
      const rightComponent = maintenanceComponents.right as any;
      if (rightComponent?.$el && typeof rightComponent.clearEffects === 'function') {
        rightComponent.clearEffects();
      }

      // 清理DOM元素
      try {
        const markers = document.querySelectorAll('.alarm-marker');
        markers?.forEach((marker) => {
          if (marker?.parentNode) {
            marker.parentNode.removeChild(marker);
          }
        });
        console.log('移除告警标记DOM元素完成');
      } catch (domError) {
        console.warn('清除告警标记DOM时出错:', domError);
      }
    } catch (error) {
      console.warn('清除智慧运维效果时出错:', error);
    }
  };

  // 监听设备详情状态变化
  watch(
    () => globalThreeStore.isDeviceDetailActive,
    (isActive) => {
      console.log(`设备详情状态变化: ${isActive ? '激活' : '关闭'}`);

      // 如果设备详情被关闭，确保左右侧栏可见
      if (!isActive) {
        if (leftBarWidth.value === '1.4vw') {
          leftBarWidth.value = '20vw';
        }
        if (rightBarWidth.value === '1.4vw') {
          rightBarWidth.value = '20vw';
        }
      }
    }
  );

  // 生命周期钩子
  onMounted(() => {
    // 初始化逻辑

    // 监听设备详情激活事件
    window.addEventListener('device-detail-activated', (event: CustomEvent) => {
      console.log('设备详情激活事件触发', event.detail);
      // 确保左右侧栏可见
      leftBarWidth.value = '20vw';
      rightBarWidth.value = '20vw';
    });

    // 监听设备详情关闭事件
    window.addEventListener('device-detail-deactivated', () => {
      console.log('设备详情关闭事件触发');
    });
  });

  onBeforeUnmount(() => {
    try {
      clearMaintenanceEffects();
      console.log('FeatureDash component unmounting, cleaning up resources');
      window.dispatchEvent(new Event('cleanup-feature-dash'));

      // 移除事件监听
      window.removeEventListener('device-detail-activated', () => {});
      window.removeEventListener('device-detail-deactivated', () => {});
    } catch (error) {
      console.warn('组件卸载清理时出错:', error);
    }
  });

  onActivated(() => {
    console.log('FeatureDash component activated');
  });

  onDeactivated(() => {
    console.log('FeatureDash component deactivated');
  });
</script>
